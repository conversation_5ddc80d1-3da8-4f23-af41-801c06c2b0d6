#!/bin/bash

# ==============================================================================
# Alastor 完整测试流程管理脚本 v2.1 (增强错误处理)
#
# 功能：
# 1. 自动部署完整应用栈 (cc-db, attackserver, functions)
# 2. 启动增强的日志收集服务器，并自动选择端口
# 3. 通过命令行参数执行指定类型的负载测试
# 4. 自动收集、整理和分析日志
# 5. 确保任何失败都有详细日志
# ==============================================================================

set -e -o pipefail

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${PURPLE}[ALASTOR]${NC} $1"; }
log_step() { echo -e "${CYAN}[STEP]${NC} $1"; }

# 🚨 全局错误处理函数
handle_error() {
    local exit_code=$?
    local lineno=$1
    local command=$2
    
    echo ""
    log_error "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
    log_error "!!         脚本执行失败，提前退出         !!"
    log_error "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
    log_error "退出代码: $exit_code"
    log_error "出错行号: $lineno"
    log_error "失败命令: $command"
    echo ""
    log_error "--- 调用堆栈 ---"
    local i=0
    local a_caller
    while a_caller=($(caller $i)); do
        if [ ${#a_caller[@]} -lt 3 ]; then break; fi
        log_error "$(printf '  -> %s:%s in function %s' "${a_caller[2]}" "${a_caller[0]}" "${a_caller[1]}")"
        i=$((i+1))
    done
    log_error "--------------------"
    
    # 脚本将在这里退出，EXIT陷阱会负责清理
}

# 🧹 全局变量存储需要清理的资源
CLEANUP_FILESERVER_PID=""
CLEANUP_KUBECTL_PIDS=()
CLEANUP_TEMP_FILES=()

# 🚨 资源清理函数
cleanup_resources() {
    local exit_code=$?
    echo "🧹 [$(date '+%H:%M:%S')] 开始清理资源..."
    
    # 停止fileserver进程
    if [[ -n "$CLEANUP_FILESERVER_PID" ]] && kill -0 "$CLEANUP_FILESERVER_PID" 2>/dev/null; then
        echo "🔌 停止fileserver进程 (PID: $CLEANUP_FILESERVER_PID)"
        kill "$CLEANUP_FILESERVER_PID" 2>/dev/null || true
        # 等待进程退出
        for i in {1..5}; do
            if ! kill -0 "$CLEANUP_FILESERVER_PID" 2>/dev/null; then
                break
            fi
            sleep 1
        done
        # 强制杀死如果还在运行
        kill -9 "$CLEANUP_FILESERVER_PID" 2>/dev/null || true
        echo "✅ fileserver进程已停止"
    fi
    
    # 停止后台kubectl进程
    for pid in "${CLEANUP_KUBECTL_PIDS[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            echo "🔌 停止kubectl进程 (PID: $pid)"
            kill "$pid" 2>/dev/null || true
        fi
    done
    
    # 清理临时文件
    for temp_file in "${CLEANUP_TEMP_FILES[@]}"; do
        if [[ -f "$temp_file" ]]; then
            echo "🗑️ 删除临时文件: $temp_file"
            rm -f "$temp_file" || true
        fi
    done
    
    # 清理端口文件
    rm -f .fileserver_port 2>/dev/null || true
    
    echo "✅ [$(date '+%H:%M:%S')] 资源清理完成 (退出代码: $exit_code)"
    # 保留退出代码
    if [ "$exit_code" -ne 0 ]; then
        log_error "脚本因错误而终止。"
    fi
}

# 🚨 注册信号处理器
# EXIT陷阱总是在脚本退出时执行，无论正常还是异常
trap cleanup_resources EXIT
# ERR陷阱在任何命令返回非零退出代码时执行 (因为 set -e)
trap 'handle_error $LINENO "$BASH_COMMAND"' ERR
# 中断和终止信号也由EXIT处理
trap 'log_warning "收到SIGINT信号，开始清理..."; exit 130' SIGINT
trap 'log_warning "收到SIGTERM信号，开始清理..."; exit 143' SIGTERM

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${PURPLE}[ALASTOR]${NC} $1"; }
log_step() { echo -e "${CYAN}[STEP]${NC} $1"; }

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# 全局配置
LOGS_BASE_DIR="$PROJECT_ROOT/logs"
DEFAULT_FILESERVER_PORT=44445
OPENFAAS_URL="http://127.0.0.1:31112"
HEY_CMD="$PROJECT_ROOT/zjy-performance/hey_linux_amd64"
PYTHON_CMD="/home/<USER>/miniconda3/envs/alastor/bin/python"  # 使用conda环境

# 测试会话配置
TEST_SESSION_ID=""
TEST_SESSION_DIR=""
FILESERVER_PID=""
FILESERVER_PORT=""
CLEANUP_REQUIRED=false

# 依赖检查
check_dependencies() {
    log_step "1/7: 检查依赖..."
    local missing_deps=()
    for cmd in kubectl faas-cli python3 curl tar; do
        if ! command -v "$cmd" &> /dev/null; then missing_deps+=("$cmd"); fi
    done
    if [ ! -f "$HEY_CMD" ] && ! command -v hey &> /dev/null; then missing_deps+=("hey"); fi
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少依赖: ${missing_deps[*]}"; exit 1
    fi
    if [ ! -f "$HEY_CMD" ]; then HEY_CMD="hey"; fi
    log_success "依赖检查完成"
}

# 生成测试会话ID
generate_session_id() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local deployment_type="$1"
    local attack_type="$2"
    local request_rate="$3"
    local duration="$4"
    
    TEST_SESSION_ID="${timestamp}_${deployment_type}_${attack_type}_${request_rate}rps"
    TEST_SESSION_DIR="$LOGS_BASE_DIR/$TEST_SESSION_ID"
    
    mkdir -p "$TEST_SESSION_DIR"
    log_info "测试会话: $TEST_SESSION_ID"
    log_info "日志目录: $TEST_SESSION_DIR"
}

# 配置环境变量
setup_environment() {
    log_step "配置环境变量..."
    
    # 获取主机IP
    local host_ip=$(hostname -I | awk '{print $1}')
    if [ -z "$host_ip" ]; then
        host_ip="*************"  # 默认值
        log_warning "无法自动获取主机IP，使用默认值: $host_ip"
    else
        log_info "检测到主机IP: $host_ip"
    fi
    
    # 保存配置到会话目录
    cat > "$TEST_SESSION_DIR/test-config.env" <<EOF
# Alastor 测试会话配置
TEST_SESSION_ID=$TEST_SESSION_ID
HOST_IP=$host_ip
FILESERVER_PORT=$FILESERVER_PORT
DEPLOYMENT_TYPE=$DEPLOYMENT_TYPE
ATTACK_TYPE=$ATTACK_TYPE
REQUEST_RATE=$REQUEST_RATE
DURATION=$DURATION
TIMESTAMP=$(date -Iseconds)
EOF
    
    export SERVER_HOST="$host_ip"
    export FILESERVER_PORT="$FILESERVER_PORT"
    
    log_success "环境配置完成"
}

# 启动文件服务器
start_fileserver() {
    log_step "2/7: 启动日志收集服务器..."
    local fileserver_script="$PROJECT_ROOT/enhanced-fileserver.py"
    if [ ! -f "$fileserver_script" ]; then
        log_error "未找到文件服务器脚本: $fileserver_script"; exit 1
    fi
    
    # 检查Python环境
    if [ ! -f "$PYTHON_CMD" ]; then
        log_warning "未找到conda Python环境，使用系统Python"
        PYTHON_CMD="python3"
    fi
    
    # 直接使用会话目录作为文件接收目录，不创建子目录
    local incoming_dir="$TEST_SESSION_DIR/raw_logs"
    mkdir -p "$incoming_dir"
    
    # 设置环境变量
    export FILESERVER_TARGET_DIR="$incoming_dir"
    export FILESERVER_PORT="$DEFAULT_FILESERVER_PORT"
    
    # 先测试能否找到可用端口
    log_info "寻找可用端口..."
    local port_test_output
    port_test_output=$($PYTHON_CMD -c "
import socket
import os
start_port = int(os.environ.get('FILESERVER_PORT', 44445))
for port in range(start_port, start_port + 100):
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', port))
            print(port)
            break
    except OSError:
        continue
else:
    print('ERROR')
" 2>/dev/null)
    
    if [ "$port_test_output" = "ERROR" ] || [ -z "$port_test_output" ]; then
        log_error "无法找到可用端口"; exit 1
    fi
    
    FILESERVER_PORT="$port_test_output"
    export FILESERVER_PORT
    log_info "将使用端口: $FILESERVER_PORT"
    
    # 在后台启动文件服务器
    log_info "启动文件服务器..."
    ($PYTHON_CMD "$fileserver_script" "$incoming_dir" > "$TEST_SESSION_DIR/fileserver.log" 2>&1) &
    FILESERVER_PID=$!
    CLEANUP_FILESERVER_PID=$FILESERVER_PID  # 🧹 注册到清理列表
    
    # 等待启动并验证
    sleep 3
    if ! kill -0 "$FILESERVER_PID" 2>/dev/null; then
        log_error "文件服务器启动失败。查看日志: $TEST_SESSION_DIR/fileserver.log"
        cat "$TEST_SESSION_DIR/fileserver.log"
        exit 1
    fi
    
    log_success "文件服务器已启动 (PID: $FILESERVER_PID, Port: $FILESERVER_PORT)"
    echo "$FILESERVER_PID" > "$TEST_SESSION_DIR/fileserver.pid"
    CLEANUP_REQUIRED=true
}

# 部署应用
deploy_application() {
    local deployment_type="$1"
    log_step "3/7: 部署应用: $deployment_type"
    
    # 部署核心服务 (DB, attackserver)
    log_info "部署核心服务..."
    # kubectl apply -f "$PROJECT_ROOT/kube-yamls/cc-db-deployment.yaml"
    # kubectl apply -f "$PROJECT_ROOT/kube-yamls/cc-db-service.yaml"
    # kubectl apply -f "$PROJECT_ROOT/kube-yamls/attackserver-deployment.yaml"
    # kubectl apply -f "$PROJECT_ROOT/kube-yamls/attackserver-service.yaml"
    kubectl apply -f "$PROJECT_ROOT/kube-yamls/cc-db.yaml"
    kubectl apply -f "$PROJECT_ROOT/kube-yamls/attackserver.yaml"
    
    # 等待核心服务就绪
    # log_info "等待核心服务启动..."
    # kubectl wait --for=condition=available --timeout=120s deployment/cc-db
    # kubectl wait --for=condition=available --timeout=120s deployment/attackserver
    
    # 部署函数
    local yaml_file=""
    case "$deployment_type" in
        "hello-retail-scale") yaml_file="$PROJECT_ROOT/kube-yamls/hello-retail.yaml";;
        "hello-retail-single") yaml_file="$PROJECT_ROOT/kube-yamls/HR-singlepod.yaml";;
        "vanilla-scale") yaml_file="$PROJECT_ROOT/kube-yamls/hello-retail-vanilla.yaml";;
        "vanilla-single") yaml_file="$PROJECT_ROOT/kube-yamls/HR-vanilla-singlepod.yaml";;
        *) log_error "未知部署类型: $deployment_type"; exit 1;;
    esac
    
    if [ ! -f "$yaml_file" ]; then log_error "部署文件不存在: $yaml_file"; exit 1; fi
    
    # 创建临时YAML文件，动态注入正确的环境变量
    local temp_yaml="$TEST_SESSION_DIR/deployment_with_env.yaml"
    local host_ip
    host_ip=$(hostname -I | awk '{print $1}')
    local server_host="${host_ip:-*************}"
    
    log_info "更新YAML文件环境变量..."
    log_info "  SERVER_HOST: $server_host"
    log_info "  FILESERVER_PORT: $FILESERVER_PORT"
    
    # 使用sed替换环境变量
    sed -e "s/SERVER_HOST: '[^']*'/SERVER_HOST: '$server_host'/g" \
        -e "s/FILESERVER_PORT: '[^']*'/FILESERVER_PORT: '$FILESERVER_PORT'/g" \
        "$yaml_file" > "$temp_yaml"
    
    log_info "部署函数: $yaml_file (已更新环境变量)"
    cp "$temp_yaml" "$TEST_SESSION_DIR/deployment.yaml"
    
    # 🔧 新增：检查是否需要部署
    log_info "检查现有函数Pod..."
    local expected_pods=4
    local running_pods
    running_pods=$(kubectl get pods -n openfaas-fn --no-headers=true 2>/dev/null | grep "product-purchase" | grep -c "Running" || true)

    if [ "$running_pods" -ge "$expected_pods" ]; then
        log_success "✓ 检测到 $running_pods 个Pod已在运行。跳过函数部署。"
    else
        log_info "检测到 $running_pods/$expected_pods 个Pod在运行，继续函数部署..."
        # 设置环境变量并部署
        export SERVER_HOST="$server_host"
        export FILESERVER_PORT
        
        log_info "执行: faas-cli deploy -f $temp_yaml"
        if ! faas-cli deploy -f "$temp_yaml"; then
            log_error "faas-cli deploy 失败！"
            log_info "这通常意味着OpenFaaS网关没有响应或YAML文件有语法错误。"
            log_info "检查faas-cli状态:"
            faas-cli list || log_warning "无法连接到OpenFaaS网关"
            exit 1 # 明确退出，将触发ERR和EXIT陷阱
        fi
        log_success "faas-cli deploy 命令执行成功"
    fi
    
    log_info "等待函数启动..."
    # 智能Pod状态检查 - 等待所有期望的pods运行
    for i in {1..45}; do # 增加超时到90秒
        # 全局错误陷阱会处理此命令的失败
        local kubectl_output
        kubectl_output=$(kubectl get pods -n openfaas-fn --no-headers=true)

        # 使用 grep -c 和 || true 来安全地计数，避免在找不到时因pipefail退出循环
        local running_pods
        running_pods=$(echo "$kubectl_output" | grep "product-purchase" | grep -c "Running" || true)
        
        echo "  [第 $i/45 次检查] Running pods: $running_pods/$expected_pods"
        
        if [ "$running_pods" -eq "$expected_pods" ]; then
            log_success "✓ 所有 $expected_pods 个Pod都已成功运行!"
            break
        fi

        # 如果Pod未全部运行，则显示详细状态以供调试
        if [ "$running_pods" -lt "$expected_pods" ]; then
            echo "    Pod当前状态详情:"
            # 显示所有相关Pod的状态，特别是那些非Running的
            echo "$kubectl_output" | grep "product-purchase" | sed 's/^/      -> /'
        fi
        
        # 检查是否已到循环末尾
        if [ $i -eq 45 ]; then
            log_error "⚠ 超时: 90秒后仍有Pod未就绪"
            log_info "这是最终的Pod状态:"
            echo "$kubectl_output" | sed 's/^/    /'
            exit 1 # 明确失败退出
        fi
        
        sleep 2
    done
    log_success "应用部署完成"
}

# a new function to collect log snapshots
collect_app_logs_snapshot() {
    log_step "5/7: 收集应用日志快照..."
    local app_logs_dir="$TEST_SESSION_DIR/app_logs"
    mkdir -p "$app_logs_dir"

    log_info "为所有函数Pod收集日志..."
    local functions=("product-purchase" "product-purchase-get-price" "product-purchase-authorize-cc" "product-purchase-publish")
    
    for func in "${functions[@]}"; do
        # 获取该函数的所有Pod名称
        local pod_names
        pod_names=$(kubectl get pods -n openfaas-fn -l "faas_function=$func" -o jsonpath='{.items[*].metadata.name}' 2>/dev/null || true)
        
        if [ -n "$pod_names" ]; then
            for pod_name in $pod_names; do
                log_info "  -> 正在收集 $func (Pod: $pod_name) 的日志..."
                kubectl logs "$pod_name" -n openfaas-fn > "$app_logs_dir/${pod_name}.log" 2>/dev/null || log_warning "    无法收集Pod $pod_name 的日志"
            done
        else
            log_warning "未找到函数 $func 的任何Pod"
        fi
    done

    # 收集核心服务日志
    log_info "收集核心服务日志..."
    local core_services=("deployment/cc-db" "deployment/attackserver")
    for service in "${core_services[@]}"; do
        local service_name
        service_name=$(basename "$service")
        log_info "  -> 正在收集 $service_name 的日志..."
        kubectl logs "$service" -n openfaas-fn > "$app_logs_dir/${service_name}.log" 2>/dev/null || log_warning "    无法收集服务 $service_name 的日志"
    done

    log_success "应用日志快照收集完成"
}

# execute_load_test function refactored
execute_load_test() {
    local attack_type="$1"
    local request_rate="$2"
    local duration="$3"
    log_step "4/7: 执行负载测试: $attack_type"
    
    local json_file="$PROJECT_ROOT/requests/$attack_type.json"
    if [ ! -f "$json_file" ]; then
        log_error "请求文件不存在: $json_file"; exit 1
    fi
    
    cp "$json_file" "$TEST_SESSION_DIR/request_payload.json"
    
    log_info "请求频率: ${request_rate} req/s, 时长: ${duration}"
    
    local target_url="$OPENFAAS_URL/function/product-purchase"
    local hey_cmd="$HEY_CMD -z $duration -q \"$request_rate\" -c 2 -m POST -D \"$json_file\" -H \"Content-Type: application/json\" \"$target_url\""
    
    # 移除旧的、有问题的实时日志收集
    
    log_info "开始负载测试..."
    eval "$hey_cmd" > "$TEST_SESSION_DIR/load_test_output.txt" 2>&1 || log_warning "负载测试命令执行失败，请检查报告。"
    
    # 负载测试完成后，等待容器完成日志上传
    log_info "负载测试完成，等待日志上传完成 (30s)..."
    sleep 30
    
    # 移除停止应用日志收集的部分
    
    log_success "负载测试阶段完成。报告: $TEST_SESSION_DIR/load_test_output.txt"
}

# cleanup_application function refactored for robustness
cleanup_application() {
    log_step "6/7: 清理应用..."
    bash $PROJECT_ROOT/tearHR.sh
    
    # local functions=("product-purchase" "product-purchase-get-price" "product-purchase-authorize-cc" "product-purchase-publish")
    
    # # 1. 删除OpenFaaS函数
    # log_info "正在删除OpenFaaS函数..."
    # faas-cli remove --filter="product-purchase" 2>/dev/null || log_warning "faas-cli remove 命令失败，可能函数已不存在"

    # # 2. 确认Kubernetes Deployment已被删除
    # log_info "等待函数Deployment被彻底删除..."
    # for func in "${functions[@]}"; do
    #     log_info "  -> 等待 ${func}..."
    #     # 使用超时来避免无限等待
    #     if ! kubectl wait --for=delete "deployment/${func}" -n openfaas-fn --timeout=60s 2>/dev/null; then
    #         log_warning "等待删除 deployment/${func} 超时或失败。可能已被删除。"
    #         # 强制删除，以防万一
    #         kubectl delete deployment "$func" -n openfaas-fn --ignore-not-found=true
    #     else
    #         log_success "  -> ${func} 已确认删除"
    #     fi
    # done
    
    # # 3. 清理核心服务
    # log_info "清理核心服务..."
    # kubectl delete -f "$PROJECT_ROOT/kube-yamls/attackserver.yaml" --ignore-not-found=true 2>/dev/null || true
    # kubectl delete -f "$PROJECT_ROOT/kube-yamls/cc-db.yaml" --ignore-not-found=true 2>/dev/null || true
    
    log_info "等待核心服务Pod终止..."
    sleep 5 # 等待Pod终止
    
    log_success "应用清理完成"
}

# collect_logs function simplified
# collect_logs() {
#     log_step "7/7: 收集和整理Alastor日志..."
    
#     local raw_logs_dir="$TEST_SESSION_DIR/raw_logs"
#     local processed_logs_dir="$TEST_SESSION_DIR/processed_logs"
    
#     mkdir -p "$processed_logs_dir"
    
#     # 应用日志已在之前的步骤中收集，这里只处理Alastor（strace/mitmproxy）日志
    
#     # 检查是否有日志文件
#     if ! ls "$raw_logs_dir"/*.tar* >/dev/null 2>&1; then
#         log_warning "未找到任何Alastor日志文件(tar)在: $raw_logs_dir"
#         log_info "检查文件服务器日志:"
#         cat "$TEST_SESSION_DIR/fileserver.log" 2>/dev/null || echo "无文件服务器日志"
#         return
#     fi
    
#     log_info "找到Alastor日志文件: $(ls -1 "$raw_logs_dir"/*.tar* | wc -l) 个"
    
#     # 解压日志文件
#     cd "$raw_logs_dir"
#     for tarfile in *.tar*; do
#         if [ ! -f "$tarfile" ]; then continue; fi
        
#         log_info "处理: $tarfile"
        
#         # 处理.tar.gz文件
#         if [[ "$tarfile" == *.tar.gz ]]; then
#             gunzip "$tarfile"
#             tarfile="${tarfile%.gz}"
#         fi
        
#         # 解压tar文件
#         if [[ "$tarfile" == *.tar ]]; then
#             # 使用Pod名称作为目录名，避免文件覆盖
#             local pod_name="${tarfile%.tar}"
#             local pod_specific_dir="$processed_logs_dir/$pod_name"
            
#             mkdir -p "$pod_specific_dir"
#             tar -xf "$tarfile" -C "$pod_specific_dir"
            
#             log_info "解压到: $pod_specific_dir"
#         fi
#     done
#     cd - >/dev/null
    
#     log_success "Alastor日志解压完成，位于: $processed_logs_dir"
    
#     local log_count=$(find "$processed_logs_dir" -type f | wc -l)
#     log_info "解压得到 $log_count 个日志文件"
# }

# analyze_logs function
analyze_logs() {
    log_step "8/8: 分析日志..." # 步骤编号增加
    
    local processor_script="$PROJECT_ROOT/alastor-log-processor.sh"
    if [ ! -f "$processor_script" ]; then
        log_warning "未找到日志处理器脚本，跳过分析"; return
    fi
    
    log_info "调用日志处理器..."
    bash "$processor_script" "$TEST_SESSION_DIR"
    log_success "日志分析完成"
}

# 旧的清理函数已被顶部的全局cleanup_resources替代
# 这里保留为兼容性，但实际不执行
legacy_cleanup_resources() {
    # 此函数已被全局cleanup_resources替代
    return 0
}

# 显示帮助
show_help() {
    cat <<EOF
Alastor 完整测试流程管理脚本 v2.2 (增强错误处理和灵活性)

用法: $0 <部署类型> <攻击类型> [请求频率] [测试时长] [--keep-app]

参数:
  部署类型:
    hello-retail-scale   - Hello Retail (可扩展)
    hello-retail-single  - Hello Retail (单Pod) [默认]
    vanilla-scale        - Vanilla版本 (可扩展)
    vanilla-single       - Vanilla版本 (单Pod)

  攻击类型:
    benign | attack1 | attack2 | cfattack | sas6 | sas3 | escape_s1 | escape_s2 | readfiles | sqlinjection | deserialize | uploadfile

  请求频率: 每秒请求数 (默认: 5)
  测试时长: 测试持续时间, 如 30s, 1m (默认: 30s)

可选标志:
  --keep-app          - 测试结束后不删除应用容器，方便调试。
  -h, --help           - 显示此帮助信息。

示例:
  $0 hello-retail-single benign
  $0 vanilla-scale cfattack 10 60s
  $0 hello-retail-single attack1 --keep-app
  $0 hello-retail-single sas6 5 30s
  $0 hello-retail-single sqlinjection 3 45s
  $0 hello-retail-single deserialize 2 60s
EOF
}

# 主函数
main() {
    # 默认值
    local keep_apps=false
    
    # 解析 --keep-app 参数
    local args=()
    for arg in "$@"; do
        if [[ "$arg" == "--keep-app" ]]; then
            keep_apps=true
        else
            args+=("$arg")
        fi
    done
    # 使用过滤后的参数覆盖原始参数列表
    set -- "${args[@]}"

    # 解析位置参数
    if [[ "$1" == "-h" || "$1" == "--help" ]]; then show_help; exit 0; fi
    if [ "$#" -lt 2 ]; then log_error "参数不足"; show_help; exit 1; fi
    
    local deployment_type="$1"
    local attack_type="$2"
    local request_rate="${3:-5}"
    local duration="${4:-30s}"
    
    # 检查是否有 'm' 或 's' 后缀
    if ! [[ "$duration" =~ [ms]$ ]]; then
        log_warning "测试时长格式不标准，建议使用 's' 或 'm' (例如 30s, 1m)。"
        log_info "将假定单位为秒: ${duration}s"
        duration="${duration}s"
    fi
    
    # 🔧 设置全局变量以便在所有函数中使用
    DEPLOYMENT_TYPE="$deployment_type"
    ATTACK_TYPE="$attack_type"
    REQUEST_RATE="$request_rate"
    DURATION="$duration"
    
    # 依赖检查
    check_dependencies
    
    # 设置会话ID
    generate_session_id "$deployment_type" "$attack_type" "$request_rate" "$duration"
    
    start_fileserver
    setup_environment
    deploy_application "$deployment_type"
    execute_load_test "$attack_type" "$request_rate" "$duration"
    
    # 新的调用顺序
    collect_app_logs_snapshot
    
    # 根据 --keep-app 标志决定是否清理
    if [ "$keep_apps" = true ]; then
        log_warning "跳过应用清理 (--keep-app 标志已设置)"
    else
        cleanup_application
    fi

    # collect_logs # 现在只收集Alastor日志
    analyze_logs
    
    log_success "测试流程完成: $TEST_SESSION_ID"
}

# 运行主函数
main "$@"