package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	parser_global "alastor/parser_global" // 从本地模块导入全局解析器
	parser_local "alastor/parser_local"

	"github.com/awalterschulze/gographviz"
)

type NodeType string

// 移除硬编码的gateway IP，改为动态获取

const (
	Container = "Container"
	Process   = "Process"
	File      = "File"
	NetPeer   = "NetPeer"
	FD        = "Fd"
)

// AlastorParser 本地容器解析器，用于生成单个容器的依赖图
type AlastorParser struct {
	containerBase    string
	containerName    string
	straceParser     *parser_local.StraceParser
	reqHandlerParser *parser_local.ReqHandlerParser
}

// NewAlastorParser 创建新的本地解析器实例
func NewAlastorParser(containerBase string) *AlastorParser {
	return &AlastorParser{
		containerBase:    containerBase,
		containerName:    containerBase[strings.LastIndex(containerBase, "/")+1:],
		straceParser:     parser_local.NewStraceParser(),
		reqHandlerParser: parser_local.NewReqHandlerParser(),
	}
}

func (p *AlastorParser) listFiles(dirPath, pattern string) ([]string, error) {
	var matchingFiles []string

	re, err := regexp.Compile(pattern)
	if err != nil {
		return nil, err
	}

	err = filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			fileName := filepath.Base(path)
			if re.MatchString(fileName) {
				matchingFiles = append(matchingFiles, path)
			}
		}

		return nil
	})

	return matchingFiles, err
}

func (p *AlastorParser) parseStrace() {
	// prepare
	pattern := `^request\.alastor\.\d+$`
	files, err := p.listFiles(p.containerBase, pattern)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	for _, file := range files {
		f, err := os.Open(file)
		if err != nil {
			fmt.Println("Error opening file:", err)
			return
		}
		pidStr := file[strings.LastIndex(file, ".")+1:]
		pid, err := strconv.Atoi(pidStr)
		if err != nil {
			fmt.Println("Error pid:", err)
			return
		}
		reader := bufio.NewReader(f)
		p.straceParser.SetPid(pid)
		p.straceParser.StartParse(reader)
		f.Close()
	}
}

func (p *AlastorParser) parseReq() {
	// prepare
	file := path.Join(p.containerBase, "request.alastor.log")
	f, err := os.Open(file)
	if err != nil {
		fmt.Println("Error opening file:", err)
		return
	}
	reader := bufio.NewReader(f)
	p.reqHandlerParser.StartParse(reader)
	f.Close()
}

func (p *AlastorParser) displayNameProcess(pid int) string {
	return p.addQuotation(Process + "##" + strconv.Itoa(pid))
}

func (p *AlastorParser) displayNameContainer(containerName string) string {
	return p.addQuotation(Container + "##" + containerName)
}

func (p *AlastorParser) displayNameFile(fileName string) string {
	return p.addQuotation(File + "##" + fileName)
}

func (p *AlastorParser) displayNetPeer(ip string, port int) string {
	inet := fmt.Sprintf("%v:%v", ip, port)
	return p.addQuotation(NetPeer + "##" + inet)
}

func (p *AlastorParser) displayFd(fd string) string {
	return p.addQuotation(FD + "##" + fd)
}

func (p *AlastorParser) getShape(nodeType NodeType) string {
	if nodeType == Container {
		return "box"
	} else if nodeType == Process {
		return "box"
	} else if nodeType == FD {
		return "box"
	} else if nodeType == File {
		return "ellipse"
	} else if nodeType == NetPeer {
		return "diamond"
	} else {
		return "ellipse"
	}
}

func (p *AlastorParser) addQuotation(str string) string {
	return fmt.Sprintf("%q", str)
}

// shouldIgnoreFile 判断是否应该忽略某个文件（过滤系统文件和库文件）
func shouldIgnoreFile(filename string) bool {
	return strings.Contains(filename, "/lib/") ||
		strings.Contains(filename, "/usr/lib/") ||
		strings.Contains(filename, "/sys/") ||
		strings.Contains(filename, "/proc/") ||
		strings.Contains(filename, "/dev/") ||
		strings.Contains(filename, "node_modules") ||
		strings.Contains(filename, "/etc/ld") ||
		strings.Contains(filename, "/etc/ssl") ||
		strings.Contains(filename, ".so") ||
		strings.HasPrefix(filename, "/usr/share/") ||
		strings.HasPrefix(filename, "/var/") ||
		filename == "/dev/null"
}

// normalizeFilePath 标准化文件路径，解决相对路径和绝对路径重复的问题
func normalizeFilePath(filename string) string {
	// 处理相对路径 ./filename -> filename
	if strings.HasPrefix(filename, "./") {
		return filename[2:]
	}

	// 处理当前目录的相对路径 ./ -> 空
	if filename == "." {
		return "."
	}

	// 其他情况保持原样
	return filename
}

// BuildLocalGraph 构建单个容器的本地依赖图
func (p *AlastorParser) BuildLocalGraph(dotPath string) {
	graphAst, _ := gographviz.Parse([]byte(`digraph G{}`))
	graph := gographviz.NewGraph()
	err := gographviz.Analyse(graphAst, graph)
	if err != nil {
		fmt.Println("Error init graph")
		return
	}

	// add container node
	nodeContainer := p.displayNameContainer(p.containerName)
	graph.AddNode("G", nodeContainer,
		map[string]string{"shape": p.addQuotation(p.getShape(Container))})

	// parse r.log
	// reqId: pid map
	reqPidMap := make(map[string]int)

	// register request handler
	p.reqHandlerParser.RegisterAllReqHook(func(msg *parser_local.ReqHandlerMessage) {
		nodeProcess := p.displayNameProcess(msg.Pid)
		reqPidMap[msg.XCallId] = msg.Pid
		graph.AddNode("G", nodeProcess,
			map[string]string{"shape": p.addQuotation(p.getShape(Process))})
		graph.AddEdge(nodeContainer, nodeProcess, true, map[string]string{
			//"label":      p.addQuotation(msg.XCallId),
			"label": fmt.Sprintf("\"%s\"", msg.XStartTime),
		})
	})
	p.parseReq()

	// parse strace
	procSyscalls := []string{"execve", "fork", "clone"}
	for _, procSyscall := range procSyscalls {
		p.straceParser.RegisterSyscallHook(procSyscall, func(msg *parser_local.StraceMessage) {
			childPid := msg.Ret
			if childPid > 0 {
				// child process generated
				//fmt.Printf("Process %v -> %v\n", msg.Pid, childPid)
				nodeParentProcess := p.displayNameProcess(msg.Pid)
				graph.AddNode("G", nodeParentProcess,
					map[string]string{"shape": p.addQuotation(p.getShape(Process))})
				nodeChildProcess := p.displayNameProcess(childPid)
				graph.AddNode("G", nodeChildProcess,
					map[string]string{"shape": p.addQuotation(p.getShape(Process))})
				graph.AddEdge(nodeParentProcess, nodeChildProcess, true, map[string]string{
					"label": fmt.Sprintf("\"%s\"", msg.Timestamp.Format("2006-01-02 15:04:05.000000")),
				})
			}
		})
	}

	rwSyscalls := []string{"read", "write"}
	for _, rwSyscall := range rwSyscalls {
		p.straceParser.RegisterSyscallHook(rwSyscall, func(msg *parser_local.StraceMessage) {
			nodeProcess := p.displayNameProcess(msg.Pid)
			fd := msg.Args[0]
			fdNum, err := strconv.Atoi(fd)
			if err != nil {
				fmt.Errorf("%v invalid fd\n", fd)
			}
			fileName := fd
			if m, ok := p.straceParser.FdMapper[msg.Pid]; ok {
				if nme, okk := m[fdNum]; okk {
					fileName = nme
				}
			}
			if fileName == "1" {
				fileName = p.displayNameFile("STDOUT")
			} else if fileName == "0" {
				fileName = p.displayNameFile("STDIN")
			} else if fileName == "2" {
				fileName = p.displayNameFile("STDERR")
			} else if !strings.Contains(fileName, "#") {
				fileName = p.displayFd(fileName)
			}
			graph.AddNode("G", nodeProcess,
				map[string]string{"shape": p.addQuotation(p.getShape(Process))})
			graph.AddNode("G", fileName,
				map[string]string{"shape": p.addQuotation(p.getShape(FD))})
			if rwSyscall == "read" {
				graph.AddEdge(fileName, nodeProcess, true, map[string]string{
					"label": fmt.Sprintf("read:\"%s\"", msg.Timestamp.Format("2006-01-02 15:04:05.000000")),
				})
			} else { // write
				graph.AddEdge(nodeProcess, fileName, true, map[string]string{
					"label": fmt.Sprintf("write:\"%s\"", msg.Timestamp.Format("2006-01-02 15:04:05.000000")),
				})
			}
		})
	}

	// 🚀 新增：处理open系统调用以生成文件节点
	p.straceParser.RegisterSyscallHook("open", func(msg *parser_local.StraceMessage) {
		if msg.Ret < 0 {
			return // 打开失败，跳过
		}

		filename := strings.Trim(msg.Args[0], "\"")

		// 跳过系统文件和库文件
		if shouldIgnoreFile(filename) {
			return
		}

		// 标准化文件路径，解决 ./sqldump.sh 和 sqldump.sh 重复的问题
		filename = normalizeFilePath(filename)

		nodeProcess := p.displayNameProcess(msg.Pid)
		nodeFile := p.displayNameFile(filename)

		// 添加进程和文件节点
		graph.AddNode("G", nodeProcess,
			map[string]string{"shape": p.addQuotation(p.getShape(Process))})
		graph.AddNode("G", nodeFile,
			map[string]string{"shape": p.addQuotation(p.getShape(File))})

		// 根据打开模式决定连接方向和标签
		openMode := ""
		if len(msg.Args) > 1 {
			openMode = msg.Args[1]
		}

		// 检查是否为写模式
		isWrite := strings.Contains(openMode, "O_WRONLY") ||
			strings.Contains(openMode, "O_RDWR") ||
			strings.Contains(openMode, "O_CREAT") ||
			strings.Contains(openMode, "O_TRUNC") ||
			strings.Contains(openMode, "O_APPEND")

		if isWrite {
			graph.AddEdge(nodeProcess, nodeFile, true, map[string]string{
				"label": fmt.Sprintf("\"open(write?):%s\"", msg.Timestamp.Format("2006-01-02 15:04:05.000000")),
				"color": "\"purple\"", // 使用特殊颜色表示可能的写操作
			})
		} else {
			graph.AddEdge(nodeFile, nodeProcess, true, map[string]string{
				"label": fmt.Sprintf("\"open(read?):%s\"", msg.Timestamp.Format("2006-01-02 15:04:05.000000")),
			})
		}

		// 将文件描述符映射到文件名
		fdNum := msg.Ret
		if fdNum >= 0 {
			p.straceParser.AddFdMap(msg.Pid, fdNum, nodeFile)
		}
	})

	// 分别处理不同类型的网络系统调用，创建socket节点

	// bind系统调用：进程绑定到socket
	p.straceParser.RegisterSyscallHook("bind", func(msg *parser_local.StraceMessage) {
		if msg.Ret < 0 {
			return // bind失败，跳过
		}

		ip, port := msg.GetInet()
		if port == 0 {
			return // 无效端口，跳过
		}

		nodeProcess := p.displayNameProcess(msg.Pid)
		graph.AddNode("G", nodeProcess,
			map[string]string{"shape": p.addQuotation(p.getShape(Process))})

		// 创建socket节点 - 使用实际的IP和端口
		socketNode := p.addQuotation(fmt.Sprintf("Socket##%s:%d", ip, port))
		graph.AddNode("G", socketNode, map[string]string{
			"shape": "diamond",
			"color": "blue",
			"label": fmt.Sprintf("\"Socket %s:%d\"", ip, port),
		})

		// 进程绑定到socket（有向边，表示绑定关系）
		graph.AddEdge(nodeProcess, socketNode, true, map[string]string{
			"label": fmt.Sprintf("\"bind %s\"", msg.Timestamp.Format("15:04:05")),
			"style": "dashed",
		})

		// 获取socket的文件描述符
		fdNum, err := strconv.Atoi(msg.Args[0])
		if err == nil && fdNum >= 0 {
			p.straceParser.AddFdMap(msg.Pid, fdNum, socketNode)
		}
	})

	// listen系统调用：socket开始监听
	p.straceParser.RegisterSyscallHook("listen", func(msg *parser_local.StraceMessage) {
		if msg.Ret < 0 {
			return // listen失败，跳过
		}

		// 从FD映射中获取socket节点
		fdNum, err := strconv.Atoi(msg.Args[0])
		if err != nil || fdNum < 0 {
			return
		}

		if fdMap, ok := p.straceParser.FdMapper[msg.Pid]; ok {
			if socketNode, exists := fdMap[fdNum]; exists {
				// 更新socket节点为监听状态
				graph.AddNode("G", socketNode, map[string]string{
					"shape": "diamond",
					"color": "green",
					"label": fmt.Sprintf("\"Listening %s\"", strings.TrimPrefix(strings.Trim(socketNode, "\""), "Socket##")),
				})
			}
		}
	})

	// connect系统调用：连接到远程端点
	p.straceParser.RegisterSyscallHook("connect", func(msg *parser_local.StraceMessage) {
		if msg.Ret < 0 {
			return // connect失败，跳过
		}

		ip, port := msg.GetInet()
		if port == 0 {
			return // 无效端口，跳过
		}

		nodeProcess := p.displayNameProcess(msg.Pid)
		graph.AddNode("G", nodeProcess,
			map[string]string{"shape": p.addQuotation(p.getShape(Process))})

		nodeInet := p.displayNetPeer(ip, port)
		graph.AddNode("G", nodeInet,
			map[string]string{"shape": p.addQuotation(p.getShape(NetPeer))})

		graph.AddEdge(nodeProcess, nodeInet, true, map[string]string{
			"label": fmt.Sprintf("\"connect %s\"", msg.Timestamp.Format("15:04:05")),
		})

		// 添加FD映射
		fdNum, err := strconv.Atoi(msg.Args[0])
		if err == nil && fdNum >= 0 {
			p.straceParser.AddFdMap(msg.Pid, fdNum, nodeInet)
		}
	})

	// accept4系统调用：从监听socket接受连接
	p.straceParser.RegisterSyscallHook("accept4", func(msg *parser_local.StraceMessage) {
		if msg.Ret < 0 {
			return // accept失败，跳过
		}

		// 获取监听socket的FD
		listenFdNum, err := strconv.Atoi(msg.Args[0])
		if err != nil || listenFdNum < 0 {
			return
		}

		nodeProcess := p.displayNameProcess(msg.Pid)
		graph.AddNode("G", nodeProcess,
			map[string]string{"shape": p.addQuotation(p.getShape(Process))})

		// 从FD映射中获取监听socket
		if fdMap, ok := p.straceParser.FdMapper[msg.Pid]; ok {
			if listenSocketNode, exists := fdMap[listenFdNum]; exists {
				// accept4系统调用表示服务器接受连接，只需要 Socket -> Process 的连边
				// 外部网络连接应该通过nettaint.log中的网络通信记录来建立
				// 这里不应该创建gateway节点，因为那是网络层的事情

				// Socket -> Process: 表示socket接受连接并传递给进程处理
				graph.AddEdge(listenSocketNode, nodeProcess, true, map[string]string{
					"label": fmt.Sprintf("\"accept %s\"", msg.Timestamp.Format("15:04:05")),
					"color": "green",
				})

				// 新连接的FD映射 - 这里应该映射到实际的网络连接，而不是硬编码的gateway
				// 实际的网络连接信息应该从nettaint.log中获取
				newConnFd := msg.Ret
				if newConnFd >= 0 {
					// 暂时使用通用的连接标识，后续可以通过网络日志关联到具体的远程地址
					connectionNode := fmt.Sprintf("Connection##%d", newConnFd)
					p.straceParser.AddFdMap(msg.Pid, newConnFd, connectionNode)
				}
			}
		}
	})

	// 其他网络系统调用
	otherNetSyscalls := []string{"sendto", "recvfrom"}
	for _, netSyscall := range otherNetSyscalls {
		p.straceParser.RegisterSyscallHook(netSyscall, func(msg *parser_local.StraceMessage) {
			ip, port := msg.GetInet()
			if port == 0 {
				return // 无效端口，跳过
			}

			nodeProcess := p.displayNameProcess(msg.Pid)
			graph.AddNode("G", nodeProcess,
				map[string]string{"shape": p.addQuotation(p.getShape(Process))})

			nodeInet := p.displayNetPeer(ip, port)
			graph.AddNode("G", nodeInet,
				map[string]string{"shape": p.addQuotation(p.getShape(NetPeer))})

			if msg.SysType == "recvfrom" {
				graph.AddEdge(nodeInet, nodeProcess, true, map[string]string{
					"label": fmt.Sprintf("\"%s %s\"", msg.SysType, msg.Timestamp.Format("15:04:05")),
				})
			} else {
				graph.AddEdge(nodeProcess, nodeInet, true, map[string]string{
					"label": fmt.Sprintf("\"%s %s\"", msg.SysType, msg.Timestamp.Format("15:04:05")),
				})
			}
		})
	}

	p.parseStrace()

	// 解析nettaint.log中的网络通信
	nettaintPath := path.Join(p.containerBase, "nettaint.log")
	if _, err := os.Stat(nettaintPath); err == nil {
		// 获取已知容器列表（这里只有当前容器，全局解析时会有完整列表）
		knownContainers := []string{p.containerName}

		// 解析nettaint日志（完全基于原始日志）
		comms, err := parseNettaintLogOnly(nettaintPath, p.containerName, knownContainers)
		if err != nil {
			fmt.Printf("解析网络日志失败 %s: %v\n", nettaintPath, err)
		} else {
			fmt.Printf("为容器 %s 解析到 %d 个网络通信记录\n", p.containerName, len(comms))

			// 将网络通信添加到图中
			for _, comm := range comms {
				fmt.Printf("处理通信记录: %s:%s -> %s:%d (%s %s)\n",
					comm.SourceIP, comm.SourcePort, comm.DestIP, comm.DestPort, comm.Method, comm.Path)

				// 构建标签：使用实际的HTTP请求内容
				var label string
				if comm.Method != "" && comm.Path != "" {
					label = fmt.Sprintf("%s %s", comm.Method, comm.Path)
				} else if comm.Method != "" {
					label = comm.Method
				} else {
					// 对于没有HTTP信息的记录，跳过（这些通常是连接建立记录）
					continue
				}

				// 创建目标网络节点
				targetNode := p.displayNetPeer(comm.DestIP, comm.DestPort)
				graph.AddNode("G", targetNode,
					map[string]string{"shape": p.addQuotation(p.getShape(NetPeer))})

				// 解析源端口
				sourcePort, err := strconv.Atoi(comm.SourcePort)
				if err != nil {
					sourcePort = 0
				}

				// 查找发起网络通信的进程
				sourceProcessPID := p.findSourceProcess(sourcePort, comm.Timestamp)

				// 设置边的属性
				edgeAttrs := map[string]string{
					"label": fmt.Sprintf("\"%s\"", label),
				}

				// 根据目标类型设置不同的可视化属性
				// 这里其实也特判了，但因为只涉及可视化，所以就没管
				if strings.Contains(comm.DestIP, "attackserver") {
					edgeAttrs["color"] = "red"
					edgeAttrs["style"] = "solid"
				} else if strings.Contains(comm.DestIP, "gateway") {
					edgeAttrs["color"] = "blue"
				} else if strings.Contains(comm.DestIP, ".svc.cluster.local") {
					edgeAttrs["color"] = "green"
				}

				// 查找源进程的监听socket
				var sourceSocketNode string
				if sourceProcessPID > 0 {
					// 查找该进程绑定的socket
					if fdMap, ok := p.straceParser.FdMapper[sourceProcessPID]; ok {
						for _, fdName := range fdMap {
							// 处理带引号和不带引号的情况
							cleanFdName := strings.Trim(fdName, "\"")
							// 查找任何Socket节点，不硬编码端口号
							if strings.HasPrefix(cleanFdName, "Socket##") {
								sourceSocketNode = fdName
								break
							}
						}
					}
				}

				if sourceSocketNode != "" {
					// 从socket连接到目标（这才是正确的连接方式）
					graph.AddNode("G", sourceSocketNode,
						map[string]string{
							"shape": "diamond",
							"color": "green",
							"label": fmt.Sprintf("\"Listening %s\"", strings.TrimPrefix(strings.Trim(sourceSocketNode, "\""), "Socket##")),
						})

					graph.AddEdge(sourceSocketNode, targetNode, true, edgeAttrs)
					fmt.Printf("添加Socket到网络边: %s -> %s (标签: %s)\n",
						sourceSocketNode, targetNode, label)
				} else {
					// 如果没找到socket，从源端点连接到目标
					sourceEndpoint := p.displayNetPeer(comm.SourceIP, sourcePort)
					graph.AddNode("G", sourceEndpoint,
						map[string]string{"shape": p.addQuotation(p.getShape(NetPeer))})

					graph.AddEdge(sourceEndpoint, targetNode, true, edgeAttrs)
					fmt.Printf("添加网络端点边: %s -> %s (标签: %s)\n",
						sourceEndpoint, targetNode, label)
				}
			}
		}
	}

	// 保存图
	fo, err := os.Create(dotPath)
	if err != nil {
		fmt.Println("Error creating file:", err)
		return
	}
	defer fo.Close()

	fo.WriteString(graph.String())
}

// findSourceProcess 查找发起网络通信的进程
func (p *AlastorParser) findSourceProcess(sourcePort int, timestamp string) int {
	// 方法1: 通过端口号在FD映射中查找
	for pid, fdMap := range p.straceParser.FdMapper {
		for _, fdName := range fdMap {
			if strings.Contains(fdName, fmt.Sprintf(":%d", sourcePort)) {
				return pid
			}
		}
	}

	// 方法2: 查找最近的connect系统调用（基于时间戳）
	// TODO: 实现基于时间戳的匹配逻辑

	// 方法3: 如果只有一个进程，直接返回
	if len(p.straceParser.FdMapper) == 1 {
		for pid := range p.straceParser.FdMapper {
			return pid
		}
	}

	return -1 // 未找到
}

// parseNettaintLogOnly 完全基于nettaint.log解析网络通信
func parseNettaintLogOnly(logPath string, containerName string, knownContainers []string) ([]*parser_global.NetworkCommunication, error) {
	file, err := os.Open(logPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var communications []*parser_global.NetworkCommunication
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := scanner.Text()

		// 跳过注释和空行
		if strings.HasPrefix(line, "#") || strings.TrimSpace(line) == "" {
			continue
		}

		// 解析HTTP请求行
		// 格式: 127.0.0.1:36916: GET http://attackserver.openfaas-fn.svc.cluster.local:8888/sqldump.sh
		httpReqRegex := regexp.MustCompile(`(\d+\.\d+\.\d+\.\d+):(\d+): (GET|POST|PUT|DELETE|HEAD|OPTIONS) http://([^/]+)(/.*)`)
		if matches := httpReqRegex.FindStringSubmatch(line); matches != nil {
			sourceIP := matches[1]
			sourcePort := matches[2]
			method := matches[3]
			hostPort := matches[4]
			path := matches[5]

			// 解析目标主机和端口
			var destIP string
			var destPort int = 80 // 默认HTTP端口

			if strings.Contains(hostPort, ":") {
				parts := strings.Split(hostPort, ":")
				destIP = parts[0]
				if port, err := strconv.Atoi(parts[1]); err == nil {
					destPort = port
				}
			} else {
				destIP = hostPort
			}

			comm := &parser_global.NetworkCommunication{
				SourceIP:      sourceIP,
				SourcePort:    sourcePort,
				SourcePod:     containerName,
				DestIP:        destIP,
				DestPort:      destPort,
				Target:        destIP,
				Method:        method,
				Path:          path,
				ContainerName: containerName,
			}

			// 判断通信类型和目标容器
			if strings.Contains(path, "/function/") {
				comm.CommType = "function_call"
				targetFunction := strings.TrimPrefix(path, "/function/")

				// 根据函数名映射到目标容器
				// 这里需要一个已知容器列表来匹配
				comm.Target = targetFunction // 暂时使用函数名作为目标

				// 如果是通过gateway的函数调用，尝试匹配目标容器
				if strings.Contains(destIP, "gateway") {
					// 尝试匹配已知容器
					for _, knownContainer := range knownContainers {
						if strings.Contains(knownContainer, targetFunction) || strings.Contains(targetFunction, knownContainer) {
							comm.DestPod = knownContainer
							comm.Target = knownContainer
							break
						}
					}
					// 如果没有匹配到，使用函数名作为目标
					if comm.DestPod == "" {
						comm.DestPod = targetFunction
					}
				}
			} else {
				comm.CommType = "external_communication"
			}

			communications = append(communications, comm)
			continue
		}

		// 解析JSON格式的网络通信记录（但只提取连接信息，不提取响应）
		if strings.HasPrefix(line, "{") {
			var record map[string]interface{}
			if err := json.Unmarshal([]byte(line), &record); err != nil {
				continue
			}

			// 跳过响应记录
			if eventType := getStringValue(record, "event_type"); eventType == "response" {
				continue
			}

			// 只处理连接建立记录
			sourceIP := getStringValue(record, "source_ip")
			sourcePort := getStringValue(record, "source_port")
			destIP := getStringValue(record, "dest_ip")
			timestamp := getStringValue(record, "timestamp")

			if sourceIP != "" && destIP != "" {
				destPort := 0
				if destPortStr := getStringValue(record, "dest_port"); destPortStr != "" {
					if port, err := strconv.Atoi(destPortStr); err == nil {
						destPort = port
					}
				}

				comm := &parser_global.NetworkCommunication{
					SourceIP:      sourceIP,
					SourcePort:    sourcePort,
					SourcePod:     containerName,
					DestIP:        destIP,
					DestPort:      destPort,
					Target:        destIP,
					CommType:      "connection",
					Timestamp:     timestamp,
					ContainerName: containerName,
				}

				communications = append(communications, comm)
			}
		}
	}

	return communications, scanner.Err()
}

// getStringFromRecord 从record中安全获取字符串值
func getStringFromRecord(record map[string]interface{}, key string) string {
	if value, ok := record[key]; ok {
		if strValue, ok := value.(string); ok {
			return strValue
		}
	}
	return ""
}

// extractHostFromURL 从URL中提取主机名和端口
func extractHostFromURL(url string) string {
	if strings.HasPrefix(url, "http://") {
		return strings.TrimPrefix(url, "http://")
	} else if strings.HasPrefix(url, "https://") {
		return strings.TrimPrefix(url, "https://")
	}
	return url
}

// saveNetworkCommunications 保存网络通信到文件，便于查看和调试
func saveNetworkCommunications(comms []*parser_global.NetworkCommunication, filePath string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入文件头
	file.WriteString("=== 网络通信解析结果 ===\n")
	file.WriteString(fmt.Sprintf("总计: %d 个网络通信\n\n", len(comms)))

	// 按类型分组统计
	funcCallCount := 0
	externalCount := 0
	otherCount := 0

	funcCalls := make([]*parser_global.NetworkCommunication, 0)
	externals := make([]*parser_global.NetworkCommunication, 0)
	others := make([]*parser_global.NetworkCommunication, 0)

	for _, comm := range comms {
		switch comm.CommType {
		case "function_call":
			funcCallCount++
			funcCalls = append(funcCalls, comm)
		case "external_communication":
			externalCount++
			externals = append(externals, comm)
		default:
			otherCount++
			others = append(others, comm)
		}
	}

	// 写入统计信息
	file.WriteString(fmt.Sprintf("容器间通信: %d 个\n", funcCallCount))
	file.WriteString(fmt.Sprintf("外部通信: %d 个\n", externalCount))
	file.WriteString(fmt.Sprintf("其他通信: %d 个\n\n", otherCount))

	// 写入容器间通信详情
	if len(funcCalls) > 0 {
		file.WriteString("=== 容器间通信详情 ===\n")
		for i, comm := range funcCalls {
			file.WriteString(fmt.Sprintf("%d. %s %s%s\n", i+1, comm.Method, comm.Target, comm.Path))
			file.WriteString(fmt.Sprintf("   源: %s\n", comm.Source))
			file.WriteString(fmt.Sprintf("   目标: %s\n", comm.Target))
			file.WriteString("\n")
		}
	}

	// 写入外部通信详情
	if len(externals) > 0 {
		file.WriteString("=== 外部通信详情 ===\n")
		for i, comm := range externals {
			file.WriteString(fmt.Sprintf("%d. %s %s%s\n", i+1, comm.Method, comm.Target, comm.Path))
			file.WriteString(fmt.Sprintf("   源: %s\n", comm.Source))
			file.WriteString(fmt.Sprintf("   外部目标: %s\n", comm.Target))
			file.WriteString(fmt.Sprintf("   路径: %s\n", comm.Path))
			file.WriteString("\n")
		}
	}

	// 写入其他通信详情
	if len(others) > 0 {
		file.WriteString("=== 其他通信详情 ===\n")
		for i, comm := range others {
			file.WriteString(fmt.Sprintf("%d. %s %s%s\n", i+1, comm.Method, comm.Target, comm.Path))
			file.WriteString(fmt.Sprintf("   源: %s\n", comm.Source))
			file.WriteString(fmt.Sprintf("   目标: %s\n", comm.Target))
			file.WriteString("\n")
		}
	}

	return nil
}

// parseAndGenerateStructuredNetworkRecord 解析mitmproxy输出并生成JSONL格式的网络记录
// 🎯 简化版本：专注于函数间通信抽象
func parseAndGenerateStructuredNetworkRecord(line, timestamp, podName string, knownContainers []string) string {
	if strings.TrimSpace(line) == "" {
		return ""
	}

	// 解析JSON格式的网络记录
	if strings.HasPrefix(line, "{") {
		var record map[string]interface{}
		if err := json.Unmarshal([]byte(line), &record); err == nil {
			// 保留原始记录的所有字段
			record["source_pod"] = podName

			// 确保必要字段存在
			if _, ok := record["communication_type"]; !ok {
				record["communication_type"] = "network_communication"
			}

			// 尝试识别函数调用
			if path, ok := record["path"].(string); ok && strings.Contains(path, "/function/") {
				record["communication_type"] = "function_call"
				targetFunction := strings.TrimPrefix(path, "/function/")
				record["target_function"] = targetFunction

				// 尝试匹配到具体的容器名
				for _, containerName := range knownContainers {
					if strings.Contains(containerName, targetFunction) {
						record["dest_pod"] = containerName
						break
					}
				}
			}

			if jsonData, err := json.Marshal(record); err == nil {
				return string(jsonData)
			}
		}
		return ""
	}

	// 解析HTTP请求记录
	httpReqRegex := regexp.MustCompile(`(\d+\.\d+\.\d+\.\d+):(\d+): (GET|POST|PUT|DELETE) http://([^/:]+(?::\d+)?)(/.*)`)
	if matches := httpReqRegex.FindStringSubmatch(line); len(matches) == 6 {
		sourceIP := matches[1]
		sourcePort := matches[2]
		method := matches[3]
		host := matches[4]
		path := matches[5]

		// 构建基本通信记录
		commRecord := map[string]interface{}{
			"timestamp":          timestamp,
			"source_pod":         podName,
			"source_ip":          sourceIP,
			"source_port":        sourcePort,
			"method":             method,
			"path":               path,
			"communication_type": "network_communication",
		}

		// 从host中提取IP和端口
		hostParts := strings.Split(host, ":")
		if len(hostParts) == 2 {
			commRecord["dest_ip"] = hostParts[0]
			if port, err := strconv.Atoi(hostParts[1]); err == nil {
				commRecord["dest_port"] = port
			}
		} else {
			commRecord["dest_ip"] = host
			commRecord["dest_port"] = 80 // 默认HTTP端口
		}

		// 检查是否是函数调用
		if strings.Contains(path, "/function/") {
			commRecord["communication_type"] = "function_call"
			targetFunction := strings.TrimPrefix(path, "/function/")
			commRecord["target_function"] = targetFunction

			// 尝试匹配到具体的容器名
			for _, containerName := range knownContainers {
				if strings.Contains(containerName, targetFunction) {
					commRecord["dest_pod"] = containerName
					break
				}
			}
		}

		if jsonData, err := json.Marshal(commRecord); err == nil {
			return string(jsonData)
		}
	}

	// 解析普通网络通信
	if strings.Contains(line, "->") || strings.Contains(line, "<-") {
		record := make(map[string]interface{})
		record["communication_type"] = "network_communication"
		record["source_pod"] = podName
		// 从实际的网络记录中提取源IP，不硬编码
		if sourceIP := getStringValue(record, "source_ip"); sourceIP != "" {
			record["source_ip"] = sourceIP
		}
		record["timestamp"] = timestamp

		// 提取源端口和目标地址
		parts := strings.Fields(line)
		for i, part := range parts {
			if strings.Contains(part, "->") || strings.Contains(part, "<-") {
				if i > 0 && i+1 < len(parts) {
					srcPort := strings.TrimSuffix(parts[i-1], ":")
					destAddr := parts[i+1]

					// 解析目标地址中的IP和端口
					destIP, destPort := parseIPPort(destAddr)

					record["source_port"] = srcPort
					if destIP != "" {
						record["dest_ip"] = destIP
					}
					if destPort != "" {
						record["dest_port"], _ = strconv.Atoi(destPort)
					}

					// 尝试从目标IP匹配已知容器
					if destIP != "" {
						for _, containerName := range knownContainers {
							// 这里可以添加更复杂的匹配逻辑，比如DNS解析等
							if strings.Contains(destIP, containerName) {
								record["dest_pod"] = containerName
								break
							}
						}
					}
				}
				break
			}
		}

		// 检查是否包含payload
		if strings.Contains(line, "Payload:") {
			payloadStart := strings.Index(line, "Payload:") + len("Payload:")
			payload := strings.TrimSpace(line[payloadStart:])
			record["payload"] = payload
			record["payload_length"] = len(payload)
		}

		// 检查是否是响应
		if strings.Contains(line, "<-") {
			record["event_type"] = "response"
		}

		if jsonData, err := json.Marshal(record); err == nil {
			return string(jsonData)
		}
	}

	return ""
}

// getStringValue 安全地从map中获取字符串值
func getStringValue(record map[string]interface{}, key string) string {
	if value, ok := record[key]; ok {
		if strValue, ok := value.(string); ok {
			return strValue
		}
		return fmt.Sprintf("%v", value)
	}
	return ""
}

// parseRequestLine 解析请求行并生成JSON记录
func parseRequestLine(line, podName string, knownContainers []string) string {
	// 示例: 127.0.0.1:36958: POST http://gateway.openfaas.svc.cluster.local:8080/function/product-purchase-publish
	parts := strings.SplitN(line, ": ", 2)
	if len(parts) != 2 {
		return ""
	}

	// 解析源地址
	sourceSocket := parts[0] // 例如: "127.0.0.1:36958"
	sourceIP, sourcePort := parseIPPort(sourceSocket)
	if sourceIP == "" || sourcePort == "" {
		return ""
	}

	// 解析请求部分
	reqParts := strings.Fields(parts[1])
	if len(reqParts) < 2 {
		return ""
	}

	method := reqParts[0] // 例如: "POST"
	url := reqParts[1]    // 例如: "http://gateway.openfaas.svc.cluster.local:8080/function/product-purchase-publish"

	// 从URL中提取主机名和路径
	targetHost, path := parseURL(url)
	targetIP, targetPort := parseIPPort(targetHost)

	// 识别目标函数和Pod
	var targetFunction, destPod string
	if strings.Contains(path, "/function/") {
		parts := strings.Split(path, "/function/")
		if len(parts) > 1 {
			targetFunction = strings.Split(parts[1], "/")[0]
			// 尝试匹配目标Pod
			for _, containerName := range knownContainers {
				if strings.Contains(containerName, targetFunction) {
					destPod = containerName
					break
				}
			}
		}
	}

	// 构建记录
	record := map[string]interface{}{
		"timestamp":          time.Now().Format("2006-01-02 15:04:05.000000"),
		"communication_type": "function_call",
		"event_type":         "http_request",
		"method":             method,
		"path":               path,
		"url":                url,
		"source_ip":          sourceIP,
		"source_port":        sourcePort,
		"source_pod":         podName,
		"target_host":        targetHost,
		"target_function":    targetFunction,
	}

	// 根据是否找到目标Pod设置不同的字段
	if destPod != "" {
		record["dest_pod"] = destPod
	} else {
		record["dest_ip"] = targetIP
		record["dest_port"] = targetPort
	}

	if jsonData, err := json.Marshal(record); err == nil {
		return string(jsonData)
	}
	return ""
}

// parseResponseLine 解析响应行并生成JSON记录
func parseResponseLine(line, podName string) string {
	// 示例: [RESPONSE] 2025-06-28 14:55:49.456 *************:8888 -> 127.0.0.1:34408 200 OK

	// 移除前缀
	content := strings.TrimPrefix(line, "[RESPONSE] ")
	parts := strings.Fields(content)

	if len(parts) < 6 {
		return "" // 格式不正确，跳过
	}

	// 解析各个部分
	timestamp := parts[0] + " " + parts[1] // "2025-06-28 14:55:49.456"
	sourceAddr := parts[2]                 // "*************:8888"
	arrow := parts[3]                      // "->"
	targetAddr := parts[4]                 // "127.0.0.1:34408"
	statusCode := parts[5]                 // "200"
	reason := ""
	if len(parts) > 6 {
		reason = strings.Join(parts[6:], " ") // "OK"
	}

	if arrow != "->" {
		return "" // 格式不正确，跳过
	}

	// 解析地址
	sourceIP, sourcePort := parseIPPort(sourceAddr)
	targetIP, targetPort := parseIPPort(targetAddr)

	// 构建响应记录
	record := map[string]interface{}{
		"timestamp":     timestamp,
		"event_type":    "http_response",
		"status_code":   statusCode,
		"reason":        reason,
		"source_ip":     sourceIP,
		"source_port":   sourcePort,
		"source_socket": sourceAddr,
		"target_ip":     targetIP,
		"target_port":   targetPort,
		"target_socket": targetAddr,
		"pod_name":      podName,
	}

	// 序列化为JSON
	if jsonData, err := json.Marshal(record); err == nil {
		return string(jsonData)
	}

	return ""
}

// parseIPPort 解析IP:Port格式的地址
func parseIPPort(addr string) (string, string) {
	parts := strings.Split(addr, ":")
	if len(parts) == 2 {
		return parts[0], parts[1]
	}
	return "", ""
}

// parseURL 从URL中提取主机名和路径
func parseURL(url string) (string, string) {
	if strings.HasPrefix(url, "http://") {
		url = strings.TrimPrefix(url, "http://")
	}
	parts := strings.SplitN(url, "/", 2)
	if len(parts) == 2 {
		return parts[0], "/" + parts[1]
	}
	return parts[0], "/"
}

// classifyNetworkCommunication 对网络通信进行分类
func classifyNetworkCommunication(url, path string) string {
	// 检查是否为函数调用：包含gateway关键字且路径包含/function/
	if (strings.Contains(url, "gateway") || strings.Contains(url, "openfaas")) && strings.Contains(path, "/function/") {
		return "function_call"
	}
	return "network_communication"
}

// extractTargetFunction 提取目标函数名
func extractTargetFunction(url, path, commType string) string {
	if commType == "function_call" && strings.Contains(path, "/function/") {
		parts := strings.Split(path, "/function/")
		if len(parts) > 1 {
			// 提取函数名（去掉可能的查询参数）
			funcName := strings.Split(parts[1], "/")[0]
			funcName = strings.Split(funcName, "?")[0]
			return funcName
		}
	}
	return ""
}

func parseMitmproxyLine(line, podName string, knownContainers []string) string {
	// Example: 127.0.0.1:36916: GET http://attackserver.openfaas-fn.svc.cluster.local:8888/sqldump.sh
	// Example: 127.0.0.1:36894: POST http://gateway.openfaas.svc.cluster.local:8080/function/product-purchase-get-price
	re := regexp.MustCompile(`\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+:\s+(GET|POST|PUT|DELETE)\s+(https?://[^\s]+)`)
	matches := re.FindStringSubmatch(line)

	if len(matches) < 3 {
		return ""
	}

	method := matches[1]
	url := matches[2]
	timestamp := time.Now().Format("2006-01-02 15:04:05.000000")

	targetHost, path := parseURL(url)
	commType := classifyNetworkCommunication(url, path)
	targetFunction := extractTargetFunction(url, path, commType)

	destPod := targetHost // Default to host
	if commType == "function_call" {
		isKnown := false
		for _, containerName := range knownContainers {
			if strings.Contains(containerName, targetFunction) {
				destPod = containerName
				isKnown = true
				break
			}
		}
		if !isKnown {
			destPod = targetFunction
		}
	}

	record := map[string]interface{}{
		"timestamp":          timestamp,
		"event_type":         "http_request",
		"communication_type": commType,
		"source_pod":         podName,
		"dest_pod":           destPod,
		"method":             method,
		"url":                url,
		"path":               path,
		"target_function":    targetFunction,
		"target_host":        targetHost,
	}

	if jsonData, err := json.Marshal(record); err == nil {
		return string(jsonData)
	}
	return ""
}

// saveStructuredNetworkCommunications 为每个容器生成结构化的JSONL格式网络通信文件
func saveStructuredNetworkCommunications(containerBaseList []string, logBase string, knownContainers []string) {
	fmt.Println("生成结构化网络通信文件(JSONL格式)...")
	fmt.Printf("已知容器列表: %v\n", knownContainers)

	for _, containerBase := range containerBaseList {
		containerName := containerBase[strings.LastIndex(containerBase, "/")+1:]
		nettaintPath := path.Join(containerBase, "nettaint.log")

		// 检查nettaint.log是否存在
		if _, err := os.Stat(nettaintPath); err != nil {
			fmt.Printf("跳过容器 %s: nettaint.log 不存在\n", containerName)
			continue
		}

		fmt.Printf("处理容器 %s 的网络日志...\n", containerName)

		// 创建结构化网络通信文件
		structuredPath := path.Join(containerBase, "network_communications.jsonl")
		structuredFile, err := os.Create(structuredPath)
		if err != nil {
			fmt.Printf("无法创建结构化网络通信文件 %s: %v\n", structuredPath, err)
			continue
		}
		defer structuredFile.Close()

		// 读取并解析nettaint.log
		file, err := os.Open(nettaintPath)
		if err != nil {
			fmt.Printf("无法打开网络日志文件 %s: %v\n", nettaintPath, err)
			continue
		}
		defer file.Close()

		scanner := bufio.NewScanner(file)
		recordCount := 0
		lineCount := 0

		for scanner.Scan() {
			line := scanner.Text()
			lineCount++

			// 使用parseAndGenerateStructuredNetworkRecord处理每一行
			timestamp := time.Now().Format(time.RFC3339)
			if record := parseAndGenerateStructuredNetworkRecord(line, timestamp, containerName, knownContainers); record != "" {
				fmt.Fprintln(structuredFile, record)
				recordCount++
				fmt.Printf("生成通信记录: %s\n", record)
			}
		}

		if err := scanner.Err(); err != nil {
			fmt.Printf("读取网络日志文件时出错 %s: %v\n", nettaintPath, err)
			continue
		}

		fmt.Printf("容器 %s 处理完成: 共处理 %d 行，生成 %d 条记录\n", containerName, lineCount, recordCount)
	}
}

func main() {
	// logBase := "static/log"
	// dotBase := "static/dot"
	if len(os.Args) < 3 {
		fmt.Println("Usage: programName <logBase> <dotBase> [--compact]")
		fmt.Println("  --compact: 生成简洁图（移除无连边的套接字等）")
		os.Exit(1)
	}

	logBase := os.Args[1]
	dotBase := os.Args[2]

	// 检查是否启用简洁模式（暂时不使用）
	_ = false // compactMode := false
	for _, arg := range os.Args[3:] {
		if arg == "--compact" {
			// compactMode = true
			fmt.Println("启用简洁图模式：将移除无连边的套接字节点")
			break
		}
	}

	containerBaseList := make([]string, 0)
	// entries, err := filepath.Glob(filepath.Join(logBase, "*-alastor-*"))
	entries, err := filepath.Glob(filepath.Join(logBase, "*"))
	fmt.Println("entries", entries)
	if err != nil {
		fmt.Println(err)
	}

	// 过滤包含alastor日志文件的目录
	validEntries := make([]string, 0)
	for _, entry := range entries {
		if fi, err := os.Stat(entry); err == nil && fi.IsDir() {
			// 检查目录中是否包含alastor日志文件
			if files, err := filepath.Glob(filepath.Join(entry, "request.alastor.*")); err == nil && len(files) > 0 {
				validEntries = append(validEntries, entry)
			}
		}
	}
	entries = validEntries

	for _, entry := range entries {
		containerBaseList = append(containerBaseList, entry)
	}
	fmt.Println("containerBaseList", containerBaseList)

	// ===== 本地图生成 (原有功能) =====
	fmt.Println("开始生成本地容器依赖图...")
	localParsers := make([]*AlastorParser, 0)
	for _, containerBase := range containerBaseList {
		parser := NewAlastorParser(containerBase)
		localParsers = append(localParsers, parser)
		parser.BuildLocalGraph(path.Join(dotBase, parser.containerName+".dot"))
		fmt.Printf("生成本地图: %s.dot\n", parser.containerName)
	}

	// ===== 全局溯源图生成 (暂时注释，使用新版本) =====
	// fmt.Println("开始生成全局溯源图...")
	// traceBuilder := parser_global.NewTraceGraphBuilder()

	// // 设置图生成模式
	// traceBuilder.SetCompactMode(compactMode)

	// // 为每个容器添加解析器
	// for _, containerBase := range containerBaseList {
	// 	fmt.Printf("添加容器解析器: %s\n", containerBase)
	// 	traceBuilder.AddContainer(containerBase)
	// }

	// 解析所有容器的日志
	// fmt.Println("解析所有容器日志...")
	// traceBuilder.ParseAllContainers()

	// 🚀 新增：解析网络通信日志
	fmt.Println("解析网络通信日志...")
	allNetworkComms := make([]*parser_global.NetworkCommunication, 0)

	// 构建已知容器名称列表
	knownContainers := make([]string, 0)
	for _, containerBase := range containerBaseList {
		containerName := containerBase[strings.LastIndex(containerBase, "/")+1:]
		knownContainers = append(knownContainers, containerName)
	}

	// 🚀 新增：首先生成结构化网络通信文件(JSONL格式)
	saveStructuredNetworkCommunications(containerBaseList, logBase, knownContainers)

	for _, containerBase := range containerBaseList {
		containerName := containerBase[strings.LastIndex(containerBase, "/")+1:]
		nettaintPath := path.Join(containerBase, "nettaint.log")
		if _, err := os.Stat(nettaintPath); err == nil {
			comms, err := parseNettaintLogOnly(nettaintPath, containerName, knownContainers)
			if err != nil {
				fmt.Printf("解析网络日志失败 %s: %v\n", nettaintPath, err)
				continue
			}

			// 保留原始的IP:port源信息，这样图形中会显示实际的网络连接
			// 不需要覆盖Source字段，因为parseNettaintLogOnly已经正确解析了IP:port

			fmt.Printf("从 %s 解析到 %d 个网络通信\n", containerBase, len(comms))
			allNetworkComms = append(allNetworkComms, comms...)
		}
	}

	// 将网络通信传递给图构建器
	// traceBuilder.SetNetworkCommunications(allNetworkComms)

	// 🚀 新增：生成网络通信格式转换文件
	networkLogPath := path.Join(dotBase, "network_communications.log")
	fmt.Printf("生成网络通信日志文件: %s\n", networkLogPath)
	if err := saveNetworkCommunications(allNetworkComms, networkLogPath); err != nil {
		fmt.Printf("保存网络通信日志失败: %v\n", err)
	}

	// 构建并保存全局图
	// globalDotPath := path.Join(dotBase, "all.dot")
	// fmt.Printf("构建全局图并保存到: %s\n", globalDotPath)
	// if compactMode {
	// 	fmt.Printf("使用简洁模式生成图形\n")
	// } else {
	// 	fmt.Printf("使用完整模式生成图形\n")
	// }
	// traceBuilder.BuildGlobalGraph(globalDotPath)

	// ===== 新版全局溯源图生成 (基于局部图) =====
	fmt.Println("开始生成基于局部图的全局溯源图...")
	localBasedBuilder := parser_global.NewLocalGraphBasedBuilder(containerBaseList, dotBase)
	if err := localBasedBuilder.GenerateGlobalGraph(); err != nil {
		fmt.Printf("生成基于局部图的全局图失败: %v\n", err)
	} else {
		fmt.Println("✅ 基于局部图的全局溯源图生成完成")
	}

	fmt.Println("Done")

	//parser.straceParser.RegisterAllSyscallHook(func(msg *parser_local.StraceMessage) {
	//	fmt.Printf("Strace: %#v \n", msg)
	//})
	//parser.parseStrace(containerBase)
}
