#!/bin/bash

# Graph 文件夹清理脚本
# 删除过时和无用的代码文件

echo "🧹 开始清理 graph 文件夹..."

# 1. 删除备份文件
echo "删除备份文件..."
rm -f graph/alastor/alastor.go.bak
rm -f graph/alastor/parser_global/trace-graph-builder-0.go.bak
rm -f graph/alastor/parser_global/trace-graph-builder-0712.go.bak

# 2. 删除空的parser-local.go文件
echo "删除空文件..."
rm -f graph/alastor/parser_local/parser-local.go

# 3. 删除过时的输出目录
echo "删除过时的输出目录..."
rm -rf graph/output-old/
rm -rf graph/output-lab-effective/
rm -rf graph/output-test/
rm -rf graph/output-train/
rm -rf graph/output_v1/
rm -rf graph/output/

# 4. 删除过时的dot文件
echo "删除过时的dot文件..."
rm -rf graph/dot/

# 5. 删除实验和测试脚本（可选，如果确定不需要）
echo "删除实验脚本..."
rm -f graph/start-lab1.sh
rm -f graph/start-log.sh
rm -f graph/run_lab.py
rm -f graph/metric.py
rm -f graph/test.py
rm -f graph/graph.sh
rm -f graph/result-lab1-effectiveness.txt

# 6. 删除原始数据和traces（可选）
echo "删除原始数据..."
rm -rf graph/raw/
rm -rf graph/traces/

# 7. 删除其他杂项文件
echo "删除杂项文件..."
rm -f graph/new_example.txt
rm -f graph/log_merge.py

echo "✅ 清理完成！"
echo ""
echo "📁 保留的核心文件："
echo "  - graph/alastor/alastor.go (主程序)"
echo "  - graph/alastor/parser_local/ (局部解析器)"
echo "  - graph/alastor/parser_global/ (全局解析器)"
echo "  - graph/alastor/internal/ (内部工具)"
echo "  - graph/generate_plots.py (可视化脚本)"
echo ""
echo "🗂️ 当前 graph 目录结构："
find graph -type f -name "*.go" -o -name "*.py" -o -name "*.md" | sort
