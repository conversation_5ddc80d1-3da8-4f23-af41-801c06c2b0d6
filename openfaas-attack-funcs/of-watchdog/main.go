package main

import (
	"bufio"
	"context"
	"flag"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	limiter "github.com/openfaas/faas-middleware/concurrency-limiter"
	"github.com/openfaas/of-watchdog/config"
	"github.com/openfaas/of-watchdog/executor"
	"github.com/openfaas/of-watchdog/metrics"
)

var (
	// 原子变量：标记是否接受新连接（1=接受，0=拒绝）
	acceptingConnections int32
	// mitmproxy 进程句柄
	cmdMitm *(exec.Cmd)
	// 日志文件句柄
	logfile *os.File
	// 网络日志文件写入互斥锁
	logMutex sync.Mutex
)

// ==============================================================================
//
//	Alastor 主程序入口 - 修复版
//
// ==============================================================================
func main() {
	fmt.Printf("[Alastor DEBUG] ========== Alastor of-watchdog 启动 (修复版) ==========\n")
	fmt.Printf("[Alastor DEBUG] 进程ID: %d\n", os.Getpid())
	fmt.Printf("[Alastor DEBUG] 启动时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	// 🔧 支持通过环境变量自定义 strace 配置
	configureStraceOptions()

	// 🔍 设置日志文件输出（重定向 watchdog 日志到文件）
	setLogOutput()
	defer logfile.Close()

	var runHealthcheck bool

	// 解析命令行参数：--run-healthcheck 用于健康检查
	flag.BoolVar(&runHealthcheck,
		"run-healthcheck",
		false,
		"Check for the a lock-file, when using an exec healthcheck. Exit 0 for present, non-zero when not found.")

	flag.Parse()

	// 如果运行健康检查模式，检查锁文件是否存在
	if runHealthcheck {
		if lockFilePresent() {
			os.Exit(0) // 锁文件存在，服务健康
		}

		fmt.Fprintf(os.Stderr, "unable to find lock file.\n")
		os.Exit(1) // 锁文件不存在，服务不健康
	}

	// 初始化连接状态为"不接受"，稍后设置为"接受"
	atomic.StoreInt32(&acceptingConnections, 0)

	// 从环境变量读取 watchdog 配置
	watchdogConfig := config.New(os.Environ())

	// 验证函数进程配置（除了静态文件服务模式）
	if len(watchdogConfig.FunctionProcess) == 0 && watchdogConfig.OperationalMode != config.ModeStatic {
		fmt.Fprintf(os.Stderr, "Provide a \"function_process\" or \"fprocess\" environmental variable for your function.\n")
		os.Exit(1)
	}

	// 🚀 第1步：同步启动mitmproxy（阻塞直到完全就绪）
	fmt.Printf("[Alastor DEBUG] 第1步：同步启动mitmproxy...\n")

	// 🔧 设置 mitmproxy 证书目录环境变量
	if os.Getenv("MITMPROXY_CONFDIR") == "" {
		os.Setenv("MITMPROXY_CONFDIR", "/tmp/mitmproxy-certs")
		fmt.Printf("[Alastor DEBUG] 设置 MITMPROXY_CONFDIR=/tmp/mitmproxy-certs\n")
	}

	mitmPort := startMitmproxySync()
	if mitmPort != "" {
		fmt.Printf("[Alastor DEBUG] ✅ mitmproxy就绪 (端口: %s)\n", mitmPort)
		// 🚀 第2步：设置全局代理环境变量
		setupProxyEnvironment(mitmPort)

		// 🚀 启动mitmproxy健康检查协程
		go checkMitmproxyHealth(mitmPort)
		fmt.Printf("[Alastor DEBUG] mitmproxy健康检查协程已启动\n")
	} else {
		fmt.Printf("[Alastor DEBUG] ⚠️ mitmproxy启动失败，继续无代理模式\n")
	}

	// 🚀 第3步：构建HTTP请求处理器（此时环境变量已设置完毕）
	fmt.Printf("[Alastor DEBUG] 第3步：构建请求处理器...\n")
	requestHandler := buildRequestHandler(watchdogConfig)

	fmt.Printf("OperationalMode: %s\n", config.WatchdogMode(watchdogConfig.OperationalMode))

	// 设置HTTP路由和指标收集
	httpMetrics := metrics.NewHttp()
	http.HandleFunc("/", metrics.InstrumentHandler(requestHandler, httpMetrics))
	http.HandleFunc("/_/health", makeHealthHandler())

	// 启动指标服务器（用于 Prometheus 监控）
	metricsServer := metrics.MetricsServer{}
	metricsServer.Register(watchdogConfig.MetricsPort)

	cancel := make(chan bool)
	go metricsServer.Serve(cancel)

	// 配置HTTP服务器参数
	shutdownTimeout := watchdogConfig.HTTPWriteTimeout
	s := &http.Server{
		Addr:           fmt.Sprintf(":%d", watchdogConfig.TCPPort),
		ReadTimeout:    watchdogConfig.HTTPReadTimeout,
		WriteTimeout:   watchdogConfig.HTTPWriteTimeout,
		MaxHeaderBytes: 1 << 20, // 最大请求头 1MB
	}

	fmt.Printf("Timeouts: read: %s, write: %s hard: %s.\n",
		watchdogConfig.HTTPReadTimeout,
		watchdogConfig.HTTPWriteTimeout,
		watchdogConfig.ExecTimeout)
	fmt.Printf("Listening on port: %d\n", watchdogConfig.TCPPort)

	// 🚀 第4步：启动HTTP服务并监听停机信号（这是一个阻塞操作）
	listenUntilShutdown(shutdownTimeout, s, watchdogConfig.SuppressLock)
}

// ==============================================================================
//
//	HTTP 请求处理器构建 - 修复版
//
// ==============================================================================
func buildRequestHandler(watchdogConfig config.WatchdogConfig) http.Handler {
	var requestHandler http.HandlerFunc

	switch watchdogConfig.OperationalMode {
	case config.ModeStreaming:
		requestHandler = makeForkRequestHandler(watchdogConfig)
		break
	case config.ModeSerializing:
		requestHandler = makeSerializingForkRequestHandler(watchdogConfig)
		break
	case config.ModeAfterBurn:
		requestHandler = makeAfterBurnRequestHandler(watchdogConfig)
		break
	case config.ModeHTTP:
		// 🚀 **关键修复**: HTTP模式必须立即启动，不能延迟！
		requestHandler = makeHTTPRequestHandler(watchdogConfig)
		break
	case config.ModeStatic:
		requestHandler = makeStaticRequestHandler(watchdogConfig)
	default:
		log.Panicf("unknown watchdog mode: %d", watchdogConfig.OperationalMode)
		break
	}

	if watchdogConfig.MaxInflight > 0 {
		return limiter.NewConcurrencyLimiter(requestHandler, watchdogConfig.MaxInflight)
	}

	return requestHandler
}

// ==============================================================================
//
//	HTTP模式处理器 - 立即启动版本（正确实现）
//
// ==============================================================================
func makeHTTPRequestHandler(watchdogConfig config.WatchdogConfig) func(http.ResponseWriter, *http.Request) {
	commandName, arguments := watchdogConfig.Process()

	// 🔧 关键修复：HTTP模式必须立即启动子进程，不能延迟！
	// 因为需要上游服务立即开始监听端口，否则请求无法到达
	fmt.Printf("[Alastor DEBUG] HTTP模式：立即启动子进程（修复延迟启动错误）\n")

	// 获取当前环境变量（此时应该包含mitmproxy设置的代理变量）
	currentEnv := os.Environ()
	fmt.Printf("[Alastor DEBUG] HTTP模式环境变量数量: %d\n", len(currentEnv))

	// 打印关键的代理环境变量用于调试
	for _, envVar := range currentEnv {
		if strings.Contains(envVar, "proxy") || strings.Contains(envVar, "PROXY") || strings.Contains(envVar, "MITM") {
			fmt.Printf("[Alastor DEBUG] HTTP模式环境变量: %s\n", envVar)
		}
	}

	functionInvoker := executor.HTTPFunctionRunner{
		ExecTimeout:    watchdogConfig.ExecTimeout,
		Process:        commandName,
		ProcessArgs:    arguments,
		BufferHTTPBody: watchdogConfig.BufferHTTPBody,
		Environment:    currentEnv, // 核心：传递包含代理设置的环境变量
	}

	if len(watchdogConfig.UpstreamURL) == 0 {
		log.Fatal(`For "mode=http" you must specify a valid URL for "http_upstream_url"`)
	}

	urlValue, upstreamURLErr := url.Parse(watchdogConfig.UpstreamURL)
	if upstreamURLErr != nil {
		log.Fatal(upstreamURLErr)
	}
	functionInvoker.UpstreamURL = urlValue

	fmt.Printf("Forking - %s %s\n", commandName, arguments)

	// 🚀 关键：立即启动子进程，不能等到第一个请求！
	err := functionInvoker.Start()
	if err != nil {
		log.Fatalf("Failed to start function: %v", err)
	}
	fmt.Printf("[Alastor DEBUG] HTTP模式：子进程启动完成\n")

	return func(w http.ResponseWriter, r *http.Request) {
		fmt.Printf("[Alastor DEBUG] === REQUEST RECEIVED === URI: %s, Method: %s\n", r.RequestURI, r.Method)

		req := executor.FunctionRequest{
			Process:      commandName,
			ProcessArgs:  arguments,
			InputReader:  r.Body,
			OutputWriter: w,
		}

		if r.Body != nil {
			defer r.Body.Close()
		}

		err := functionInvoker.Run(req, r.ContentLength, r, w)
		if err != nil {
			fmt.Printf("[Alastor DEBUG] === REQUEST FAILED === Error: %v\n", err)
			w.WriteHeader(500)
			w.Write([]byte(err.Error()))
		}
		fmt.Printf("[Alastor DEBUG] === REQUEST COMPLETED === URI: %s\n", r.RequestURI)
	}
}

// ==============================================================================
//
//	其他模式处理器 - 保持原有逻辑
//
// ==============================================================================
func makeAfterBurnRequestHandler(watchdogConfig config.WatchdogConfig) func(http.ResponseWriter, *http.Request) {
	commandName, arguments := watchdogConfig.Process()
	functionInvoker := executor.AfterBurnFunctionRunner{
		Process:     commandName,
		ProcessArgs: arguments,
	}

	fmt.Printf("Forking %s %s\n", commandName, arguments)
	functionInvoker.Start()

	return func(w http.ResponseWriter, r *http.Request) {
		req := executor.FunctionRequest{
			Process:      commandName,
			ProcessArgs:  arguments,
			InputReader:  r.Body,
			OutputWriter: w,
		}

		functionInvoker.Mutex.Lock()
		err := functionInvoker.Run(req, r.ContentLength, r, w)
		if err != nil {
			w.WriteHeader(500)
			w.Write([]byte(err.Error()))
		}
		functionInvoker.Mutex.Unlock()
	}
}

func makeSerializingForkRequestHandler(watchdogConfig config.WatchdogConfig) func(http.ResponseWriter, *http.Request) {
	functionInvoker := executor.SerializingForkFunctionRunner{
		ExecTimeout: watchdogConfig.ExecTimeout,
	}

	return func(w http.ResponseWriter, r *http.Request) {
		var environment []string
		if watchdogConfig.InjectCGIHeaders {
			environment = getEnvironment(r)
		}

		commandName, arguments := watchdogConfig.Process()
		req := executor.FunctionRequest{
			Process:       commandName,
			ProcessArgs:   arguments,
			InputReader:   r.Body,
			ContentLength: &r.ContentLength,
			OutputWriter:  w,
			Environment:   environment,
		}

		w.Header().Set("Content-Type", watchdogConfig.ContentType)
		err := functionInvoker.Run(req, w)
		if err != nil {
			log.Println(err)
		}
	}
}

func makeForkRequestHandler(watchdogConfig config.WatchdogConfig) func(http.ResponseWriter, *http.Request) {
	functionInvoker := executor.ForkFunctionRunner{
		ExecTimeout: watchdogConfig.ExecTimeout,
	}

	return func(w http.ResponseWriter, r *http.Request) {
		var environment []string
		if watchdogConfig.InjectCGIHeaders {
			environment = getEnvironment(r)
		}

		commandName, arguments := watchdogConfig.Process()
		req := executor.FunctionRequest{
			Process:      commandName,
			ProcessArgs:  arguments,
			InputReader:  r.Body,
			OutputWriter: w,
			Environment:  environment,
		}

		w.Header().Set("Content-Type", watchdogConfig.ContentType)
		err := functionInvoker.Run(req)
		if err != nil {
			log.Println(err.Error())
		}
	}
}

func getEnvironment(r *http.Request) []string {
	// 继承父进程的所有环境变量（包括代理设置）
	envs := os.Environ()

	// 验证代理环境变量是否存在
	mitmPort := os.Getenv("ALASTOR_MITM_PORT")
	if mitmPort != "" {
		fmt.Printf("[Alastor DEBUG] getEnvironment(): 检测到ALASTOR_MITM_PORT=%s\n", mitmPort)
	} else {
		fmt.Printf("[Alastor DEBUG] getEnvironment(): 警告 - ALASTOR_MITM_PORT未设置\n")
	}

	// 添加CGI风格的HTTP头
	for k, v := range r.Header {
		kv := fmt.Sprintf("Http_%s=%s", strings.Replace(k, "-", "_", -1), v[0])
		envs = append(envs, kv)
	}
	envs = append(envs, fmt.Sprintf("Http_Method=%s", r.Method))

	if len(r.URL.RawQuery) > 0 {
		envs = append(envs, fmt.Sprintf("Http_Query=%s", r.URL.RawQuery))
	}
	if len(r.URL.Path) > 0 {
		envs = append(envs, fmt.Sprintf("Http_Path=%s", r.URL.Path))
	}
	if len(r.TransferEncoding) > 0 {
		envs = append(envs, fmt.Sprintf("Http_Transfer_Encoding=%s", r.TransferEncoding[0]))
	}

	return envs
}

func makeStaticRequestHandler(watchdogConfig config.WatchdogConfig) http.HandlerFunc {
	if watchdogConfig.StaticPath == "" {
		log.Fatal(`For mode=static you must specify the "static_path" to serve`)
	}
	fmt.Printf("Serving files at %s", watchdogConfig.StaticPath)
	return http.FileServer(http.Dir(watchdogConfig.StaticPath)).ServeHTTP
}

// ==============================================================================
//
//	同步mitmproxy启动函数 - 核心修复
//
// ==============================================================================
func startMitmproxySync() string {
	fmt.Printf("[Alastor DEBUG] ========== 同步启动mitmproxy ==========\n")

	// 检查mitmproxy是否可用
	mitmdumpPath, err := exec.LookPath("mitmdump")
	if err != nil {
		fmt.Printf("[Alastor DEBUG] ❌ mitmdump未找到: %v\n", err)
		return ""
	}
	fmt.Printf("[Alastor DEBUG] ✅ mitmdump路径: %s\n", mitmdumpPath)

	// 选择端口
	mitmPort := selectMitmproxyPort()
	if mitmPort == "" || mitmPort == "0" {
		fmt.Printf("[Alastor DEBUG] ❌ 无可用端口\n")
		return ""
	}

	// 配置证书目录
	confDir := os.Getenv("MITMPROXY_CONFDIR")
	if confDir == "" {
		confDir = "/tmp/mitmproxy-certs" // 使用临时目录，确保有写权限
	}

	// 确保证书目录存在且有正确权限
	if err := os.MkdirAll(confDir, 0755); err != nil {
		fmt.Printf("[Alastor DEBUG] ❌ 无法创建证书目录 %s: %v\n", confDir, err)
		return ""
	}

	// 验证目录权限
	testFile := filepath.Join(confDir, "test-write-permission")
	if err := ioutil.WriteFile(testFile, []byte("test"), 0644); err != nil {
		fmt.Printf("[Alastor DEBUG] ❌ 证书目录无写权限 %s: %v\n", confDir, err)
		return ""
	}
	os.Remove(testFile) // 清理测试文件
	fmt.Printf("[Alastor DEBUG] ✅ 证书目录权限验证通过: %s\n", confDir)

	// 启动mitmproxy - 增强参数以获取更详细的网络信息
	mitmScriptPath := createMitmproxyScript(confDir)
	cmdArgs := []string{
		"--listen-port", mitmPort,
		"--listen-host", "127.0.0.1",
		"--showhost",                  // 显示主机信息
		"--set", "block_global=false", // 允许全局连接
		"--set", fmt.Sprintf("confdir=%s", confDir), // 设置证书目录
		"--verbose",          // 启用详细输出
		"-s", mitmScriptPath, // 使用自定义脚本输出结构化信息
		"--anticache",                       // 禁用缓存，确保所有请求都被记录
		"--anticomp",                        // 禁用压缩，便于查看内容
		"--set", "stream_large_bodies=1024", // 流式处理大文件
		"--set", "tcp_nodelay=true", // 禁用TCP Nagle算法，获取更准确的时序
		"--set", "keep_host_header=true", // 保持原始Host头
		"--set", "connection_strategy=eager", // 积极建立连接策略
	}

	fmt.Printf("[Alastor DEBUG] 启动命令: mitmdump %s\n", strings.Join(cmdArgs, " "))
	cmdMitm = exec.Command("mitmdump", cmdArgs...)

	// 创建日志文件
	logPath := "nettaint.log"
	f, err := os.Create(logPath)
	if err != nil {
		fmt.Printf("[Alastor DEBUG] ❌ 无法创建nettaint.log: %v\n", err)
		return ""
	}
	fmt.Printf("[Alastor DEBUG] ✅ 网络日志文件已创建: %s\n", logPath)

	// 写入初始标记
	fmt.Fprintf(f, "# Alastor mitmproxy 网络日志 - 启动时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Fprintf(f, "# mitmproxy 端口: %s\n", mitmPort)
	fmt.Fprintf(f, "# =====================================\n")
	f.Sync() // 强制刷新到磁盘

	// 设置输出管道 - 同时捕获 stdout 和 stderr
	stdout, err := cmdMitm.StdoutPipe()
	if err != nil {
		fmt.Printf("[Alastor DEBUG] ❌ 无法创建stdout管道: %v\n", err)
		f.Close()
		return ""
	}

	stderr, err := cmdMitm.StderrPipe()
	if err != nil {
		fmt.Printf("[Alastor DEBUG] ❌ 无法创建stderr管道: %v\n", err)
		f.Close()
		return ""
	}

	// 启动日志处理协程 - 处理 stdout，增强网络日志信息
	w := bufio.NewWriter(f)
	scanner := bufio.NewScanner(stdout)

	go func() {
		defer f.Close()
		defer w.Flush()
		fmt.Printf("[Alastor DEBUG] mitmproxy stdout日志处理器启动\n")
		for scanner.Scan() {
			line := scanner.Text()
			logMutex.Lock()

			// ‼️ **核心改动**: 直接将mitmproxy脚本的输出写入日志文件
			// 不再进行二次解析和格式化，以保留完整的JSON信息
			fmt.Fprintln(w, line)
			w.Flush()

			logMutex.Unlock()

			// 保留对 "listening" 消息的调试输出
			if strings.Contains(line, "listening") {
				fmt.Printf("[Alastor DEBUG] mitmproxy: %s\n", line)
			}
		}
		fmt.Printf("[Alastor DEBUG] mitmproxy stdout日志处理器退出\n")
	}()

	// 启动错误日志处理协程 - 处理 stderr
	stderrScanner := bufio.NewScanner(stderr)
	go func() {
		fmt.Printf("[Alastor DEBUG] mitmproxy stderr日志处理器启动\n")
		for stderrScanner.Scan() {
			line := stderrScanner.Text()
			fmt.Printf("[Alastor ERROR] mitmproxy stderr: %s\n", line)
			// 同时写入日志文件
			logMutex.Lock()
			fmt.Fprintln(w, fmt.Sprintf("[STDERR] %s", line))
			w.Flush()
			logMutex.Unlock()
		}
		fmt.Printf("[Alastor DEBUG] mitmproxy stderr日志处理器退出\n")
	}()

	// 添加更多调试信息
	fmt.Printf("[Alastor DEBUG] 启动前检查 - 端口 %s 可用性: %v\n", mitmPort, isPortAvailable(mitmPort))
	fmt.Printf("[Alastor DEBUG] 证书目录: %s\n", confDir)

	// 检查证书目录
	if _, err := os.Stat(confDir); os.IsNotExist(err) {
		fmt.Printf("[Alastor DEBUG] ⚠️ 证书目录不存在，正在创建: %s\n", confDir)
	} else {
		fmt.Printf("[Alastor DEBUG] ✅ 证书目录已存在: %s\n", confDir)
	}

	// 启动进程
	if err := cmdMitm.Start(); err != nil {
		fmt.Printf("[Alastor DEBUG] ❌ mitmproxy启动失败: %v\n", err)
		return ""
	}

	// 🚀 **关键修复**: 启动goroutine监控mitmproxy进程状态
	go func() {
		err := cmdMitm.Wait()
		if err != nil {
			fmt.Printf("[Alastor CRITICAL] mitmproxy进程异常退出，错误: %v\n", err)
		} else {
			fmt.Printf("[Alastor CRITICAL] mitmproxy进程正常退出\n")
		}
		// 不立即终止，而是记录错误，让容器继续运行但标记为不健康
		fmt.Printf("[Alastor CRITICAL] mitmproxy已退出，网络日志收集将停止\n")
	}()

	// 🚀 **关键**: 同步等待端口就绪
	fmt.Printf("[Alastor DEBUG] 等待mitmproxy端口%s就绪...\n", mitmPort)
	timeout := time.After(15 * time.Second)
	tick := time.Tick(200 * time.Millisecond)

	attemptCount := 0
	for {
		select {
		case <-timeout:
			fmt.Printf("[Alastor DEBUG] ❌ 等待超时，尝试次数: %d\n", attemptCount)
			if cmdMitm.Process != nil {
				fmt.Printf("[Alastor DEBUG] 终止mitmproxy进程 (PID: %d)\n", cmdMitm.Process.Pid)
				cmdMitm.Process.Kill()
			}
			return ""
		case <-tick:
			attemptCount++
			conn, err := net.DialTimeout("tcp", "127.0.0.1:"+mitmPort, 200*time.Millisecond)
			if err == nil {
				conn.Close()
				fmt.Printf("[Alastor DEBUG] ✅ mitmproxy就绪 (PID: %d, 端口: %s, 尝试次数: %d)\n", cmdMitm.Process.Pid, mitmPort, attemptCount)

				// 进行一次额外的健康检查
				time.Sleep(100 * time.Millisecond)
				healthConn, healthErr := net.DialTimeout("tcp", "127.0.0.1:"+mitmPort, 200*time.Millisecond)
				if healthErr != nil {
					fmt.Printf("[Alastor DEBUG] ⚠️ 健康检查失败，继续等待: %v\n", healthErr)
					continue
				}
				healthConn.Close()
				fmt.Printf("[Alastor DEBUG] ✅ 健康检查通过\n")

				return mitmPort
			} else {
				if attemptCount%10 == 0 { // 每10次尝试打印一次状态
					fmt.Printf("[Alastor DEBUG] 端口连接尝试 %d: %v\n", attemptCount, err)
				}
			}
		}
	}
}

// setupProxyEnvironment 设置代理环境变量
func setupProxyEnvironment(mitmPort string) {
	proxyURL := fmt.Sprintf("http://127.0.0.1:%s", mitmPort)
	fmt.Printf("[Alastor DEBUG] 设置代理环境变量: %s\n", proxyURL)

	os.Setenv("http_proxy", proxyURL)
	os.Setenv("HTTP_PROXY", proxyURL)
	os.Setenv("https_proxy", proxyURL)
	os.Setenv("HTTPS_PROXY", proxyURL)
	os.Setenv("NODE_TLS_REJECT_UNAUTHORIZED", "0")
	os.Setenv("ALASTOR_MITM_PORT", mitmPort)

	// 验证设置
	fmt.Printf("[Alastor DEBUG] 环境变量验证:\n")
	fmt.Printf("  http_proxy: %s\n", os.Getenv("http_proxy"))
	fmt.Printf("  https_proxy: %s\n", os.Getenv("https_proxy"))
	fmt.Printf("  NODE_TLS_REJECT_UNAUTHORIZED: %s\n", os.Getenv("NODE_TLS_REJECT_UNAUTHORIZED"))
}

// selectMitmproxyPort 选择可用端口
func selectMitmproxyPort() string {
	if envPort := os.Getenv("MITM_PORT"); envPort != "" {
		return envPort
	}

	preferredPort := "18080"
	if isPortAvailable(preferredPort) {
		return preferredPort
	}

	for port := 18081; port <= 18100; port++ {
		portStr := fmt.Sprintf("%d", port)
		if isPortAvailable(portStr) {
			return portStr
		}
	}

	return ""
}

// isPortAvailable 检查端口是否可用
func isPortAvailable(port string) bool {
	conn, err := net.DialTimeout("tcp", "127.0.0.1:"+port, 100*time.Millisecond)
	if err != nil {
		return true
	}
	conn.Close()
	return false
}

// checkMitmproxyHealth 定期检查mitmproxy健康状态
func checkMitmproxyHealth(mitmPort string) {
	if mitmPort == "" {
		return
	}

	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			conn, err := net.DialTimeout("tcp", "127.0.0.1:"+mitmPort, 1*time.Second)
			if err != nil {
				fmt.Printf("[Alastor WARNING] mitmproxy健康检查失败 (端口 %s): %v\n", mitmPort, err)
				// 检查进程是否还存在
				if cmdMitm != nil && cmdMitm.Process != nil {
					// 发送信号0来检查进程是否存在
					if err := cmdMitm.Process.Signal(syscall.Signal(0)); err != nil {
						fmt.Printf("[Alastor CRITICAL] mitmproxy进程已死亡: %v\n", err)
					} else {
						fmt.Printf("[Alastor WARNING] mitmproxy进程存在但端口不响应\n")
					}
				}
			} else {
				conn.Close()
				fmt.Printf("[Alastor DEBUG] mitmproxy健康检查通过 (端口 %s)\n", mitmPort)
			}
		}
	}
}

// extractConnectionInfo 提取连接信息，包括源IP、端口等
func extractConnectionInfo(line string) string {
	// 从mitmproxy日志行中提取源连接信息
	// 示例: "127.0.0.1:53732: GET http://attackserver..."
	if idx := strings.Index(line, ":"); idx != -1 {
		if colonIdx := strings.Index(line[idx+1:], ":"); colonIdx != -1 {
			sourceIPPort := strings.TrimSpace(line[:idx+1+colonIdx])
			return fmt.Sprintf("Source=%s", sourceIPPort)
		}
	}
	return "Source=unknown"
}

// extractSize 提取响应大小信息
func extractSize(line string) string {
	// 查找类似 "91b" 或 "200 OK 91b" 的模式
	if strings.Contains(line, "b") {
		parts := strings.Fields(line)
		for _, part := range parts {
			if strings.HasSuffix(part, "b") && len(part) > 1 {
				return part
			}
		}
	}
	return "unknown"
}

// extractHeaders 提取关键头信息
func extractHeaders(line string) string {
	headers := []string{}

	// 检查常见的HTTP头部关键词
	if strings.Contains(line, "Content-Type:") {
		if idx := strings.Index(line, "Content-Type:"); idx != -1 {
			remaining := line[idx:]
			if end := strings.Index(remaining, "\n"); end != -1 {
				headers = append(headers, strings.TrimSpace(remaining[:end]))
			}
		}
	}

	if strings.Contains(line, "Content-Length:") {
		if idx := strings.Index(line, "Content-Length:"); idx != -1 {
			remaining := line[idx:]
			if end := strings.Index(remaining, "\n"); end != -1 {
				headers = append(headers, strings.TrimSpace(remaining[:end]))
			}
		}
	}

	if len(headers) > 0 {
		return strings.Join(headers, "; ")
	}
	return "none"
}

// ==============================================================================
//
//	其他辅助函数 - 保持原有逻辑
//
// ==============================================================================
func markUnhealthy() error {
	atomic.StoreInt32(&acceptingConnections, 0)
	path := filepath.Join(os.TempDir(), ".lock")
	fmt.Printf("Removing lock-file : %s\n", path)
	removeErr := os.Remove(path)
	return removeErr
}

func listenUntilShutdown(shutdownTimeout time.Duration, s *http.Server, suppressLock bool) {
	idleConnsClosed := make(chan struct{})

	go func() {
		sig := make(chan os.Signal, 1)
		signal.Notify(sig, syscall.SIGTERM)

		fmt.Printf("[Alastor DEBUG] Shutdown handler registered. Waiting for SIGTERM.\n")
		<-sig

		fmt.Printf("[Alastor DEBUG] SIGTERM received. Starting graceful shutdown sequence...\n")
		fmt.Printf("SIGTERM received.. shutting down server in %s\n", shutdownTimeout.String())

		fmt.Println("[Alastor DEBUG] Calling sendLogs() before shutdown.")
		sendLogs()
		fmt.Println("[Alastor DEBUG] sendLogs() finished.")

		healthErr := markUnhealthy()
		if healthErr != nil {
			fmt.Printf("Unable to mark unhealthy during shutdown: %s\n", healthErr.Error())
		}

		<-time.Tick(shutdownTimeout)

		if err := s.Shutdown(context.Background()); err != nil {
			fmt.Printf("Error in Shutdown: %v", err)
		}

		fmt.Printf("No new connections allowed. Exiting in: %s\n", shutdownTimeout.String())
		<-time.Tick(shutdownTimeout)

		close(idleConnsClosed)
	}()

	go func() {
		if err := s.ListenAndServe(); err != http.ErrServerClosed {
			fmt.Printf("Error ListenAndServe: %v", err)
			close(idleConnsClosed)
		}
	}()

	if suppressLock == false {
		path, writeErr := createLockFile()
		if writeErr != nil {
			log.Panicf("Cannot write %s. To disable lock-file set env suppress_lock=true.\n Error: %s.\n", path, writeErr.Error())
		}
	} else {
		log.Println("Warning: \"suppress_lock\" is enabled. No automated health-checks will be in place for your function.")
		atomic.StoreInt32(&acceptingConnections, 1)
	}

	<-idleConnsClosed
}

func createLockFile() (string, error) {
	path := filepath.Join(os.TempDir(), ".lock")
	fmt.Printf("Writing lock-file to: %s\n", path)

	mkdirErr := os.MkdirAll(os.TempDir(), os.ModePerm)
	if mkdirErr != nil {
		return path, mkdirErr
	}

	writeErr := ioutil.WriteFile(path, []byte{}, 0660)
	if writeErr != nil {
		return path, writeErr
	}

	atomic.StoreInt32(&acceptingConnections, 1)
	return path, nil
}

func lockFilePresent() bool {
	path := filepath.Join(os.TempDir(), ".lock")
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return false
	}
	return true
}

func makeHealthHandler() func(http.ResponseWriter, *http.Request) {
	return func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case http.MethodGet:
			if atomic.LoadInt32(&acceptingConnections) == 0 || lockFilePresent() == false {
				w.WriteHeader(http.StatusServiceUnavailable)
				return
			}
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("OK"))
			break
		default:
			w.WriteHeader(http.StatusMethodNotAllowed)
		}
	}
}

func setLogOutput() {
	logfile, err := os.OpenFile("request.alastor.log", os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0644)
	if err != nil {
		log.Fatal(err)
	}
	log.SetOutput(logfile)
	log.Println("Logging to a file in Go!")
}

func getHostIP() string {
	if envIP := os.Getenv("SERVER_HOST"); envIP != "" {
		return envIP
	}

	fallbackIP := os.Getenv("SERVER_HOST_FALLBACK")
	if fallbackIP == "" {
		fallbackIP = "*************"
	}
	return fallbackIP
}

func sendLogs() {
	fmt.Println("[Alastor DEBUG] Entered sendLogs() function.")

	straceFiles, err := filepath.Glob("request.alastor*")
	if err != nil {
		fmt.Printf("[Alastor DEBUG] Error when searching for strace log files: %v\n", err)
		return
	}

	var mitmFiles []string
	if _, err := os.Stat("nettaint.log"); err == nil {
		mitmFiles = append(mitmFiles, "nettaint.log")
		fmt.Printf("[Alastor DEBUG] Found mitmproxy log: nettaint.log\n")
	}
	if _, err := os.Stat("detailed-requests.dump"); err == nil {
		mitmFiles = append(mitmFiles, "detailed-requests.dump")
		fmt.Printf("[Alastor DEBUG] Found detailed requests dump: detailed-requests.dump\n")
	}

	allLogFiles := append(straceFiles, mitmFiles...)
	if len(allLogFiles) == 0 {
		fmt.Println("[Alastor DEBUG] No log files found (strace or mitmproxy). Skipping log send.")
		return
	}

	hostIP := getHostIP()
	fileserverPort := os.Getenv("FILESERVER_PORT")
	if fileserverPort == "" {
		fileserverPort = "44444"
	}

	podNameBytes, err := ioutil.ReadFile("/etc/hostname")
	if err != nil {
		fmt.Printf("[Alastor DEBUG] Failed to read /etc/hostname: %v\n", err)
		return
	}

	podstr := strings.TrimSpace(string(podNameBytes))
	tarFile := fmt.Sprintf("%s.tar.gz", podstr)

	if len(allLogFiles) > 0 {
		tarCmd := fmt.Sprintf("tar -czf %s %s", tarFile, strings.Join(allLogFiles, " "))
		sendlogCmd := fmt.Sprintf("curl -v -F 'file=@%s' http://%s:%s/", tarFile, hostIP, fileserverPort)
		fullCmd := fmt.Sprintf("%s && %s", tarCmd, sendlogCmd)

		cmd := exec.Command("/bin/sh", "-c", fullCmd)
		output, err := cmd.CombinedOutput()

		if err != nil {
			fmt.Printf("[Alastor DEBUG] Log send command FAILED. Error: %v\n", err)
			fmt.Printf("[Alastor DEBUG] Command output:\n---\n%s\n---\n", string(output))
		} else {
			fmt.Printf("[Alastor DEBUG] Log send command SUCCEEDED.\n")
		}
	}
}

func configureStraceOptions() {
	fmt.Println("[Alastor DEBUG] 🔧 配置 strace 选项")
	// 7.2 添加关键的 read,write 系统调用；添加更多syscall
	// defaultArgs := "-ttt -q -o request.alastor -e trace=read,write,execve,fork,clone,open,socket,bind,listen,accept4,connect,sendto,recvfrom,chmod,chown,access,unlink,unlinkat -ff"
	defaultArgs := "-ttt -q -o request.alastor -e trace=read,write,writev,execve,fork,clone,exit_group,socket,bind,listen,accept4,connect,sendto,recvfrom,chmod,chown,access,unlink,unlinkat,open,openat,close,stat,lstat,mmap,munmap,ioctl,epoll_ctl,epoll_wait,setsockopt,getsockopt,prctl -ff"

	fmt.Printf("[Alastor DEBUG] 使用默认 strace 参数: %s\n", defaultArgs)
}

// copyFile 复制文件
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	return err
}

// ‼️ **核心改动**: 将 Python 脚本内容直接内嵌到 Go 程序中。
// 这样可以避免因路径问题找不到脚本，确保 mitmproxy 始终使用增强版脚本。
const alastorEnhancedScriptContent = `#!/usr/bin/env python3
"""
Alastor 简化版 mitmproxy Script
只收集用户需要的网络字段：时间戳、源IP、源端口、目的IP、目的端口、载荷长度、载荷
"""
import json
import sys
from datetime import datetime
import time
from mitmproxy import http

def get_client_info(flow):
    """安全获取客户端连接信息 - 兼容不同版本的mitmproxy API"""
    try:
        if hasattr(flow, 'client_conn') and flow.client_conn:
            if hasattr(flow.client_conn, 'peername') and flow.client_conn.peername:
                return flow.client_conn.peername[0], flow.client_conn.peername[1]
            if hasattr(flow.client_conn, 'address') and flow.client_conn.address:
                return flow.client_conn.address[0], flow.client_conn.address[1]
    except:
        pass
    return "127.0.0.1", 0

def request(flow: http.HTTPFlow) -> None:
    """处理HTTP请求"""
    try:
        req = flow.request
        
        # 获取连接信息
        source_ip, source_port = get_client_info(flow)
        dest_ip = req.pretty_host
        dest_port = req.port
        
        # 获取请求载荷
        payload = req.content.decode('utf-8', errors='ignore') if req.content else ""
        
        log_entry = {
            "timestamp": datetime.fromtimestamp(time.time()).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
            "source_ip": source_ip,
            "source_port": source_port,
            "dest_ip": dest_ip,
            "dest_port": dest_port,
            "payload_length": len(payload),
            "payload": payload
        }
        
        sys.stdout.write(f"{json.dumps(log_entry)}\n")
        sys.stdout.flush()
        
    except Exception as e:
        # 静默处理错误
        sys.stderr.write(f"[ERROR] Request处理异常: {e}\n")
        sys.stderr.flush()

def response(flow: http.HTTPFlow) -> None:
    """处理HTTP响应"""
    try:
        req = flow.request
        resp = flow.response
        
        if resp:
            # 获取连接信息
            source_ip, source_port = get_client_info(flow)
            dest_ip = req.pretty_host
            dest_port = req.port
            
            # 获取响应载荷
            payload = resp.content.decode('utf-8', errors='ignore') if resp.content else ""
            
            log_entry = {
                "timestamp": datetime.fromtimestamp(time.time()).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
                "source_ip": source_ip,
                "source_port": source_port,
                "dest_ip": dest_ip,
                "dest_port": dest_port,
                "payload_length": len(payload),
                "payload": payload,
                "event_type": "response"
            }
            
            sys.stdout.write(f"{json.dumps(log_entry)}\n")
            sys.stdout.flush()
            
    except Exception as e:
        # 静默处理错误
        sys.stderr.write(f"[ERROR] Response处理异常: {e}\n")
        sys.stderr.flush()
`

// createMitmproxyScript 查找或复制mitmproxy增强脚本
func createMitmproxyScript(confDir string) string {
	// ‼️ **核心改动**: 不再从文件系统查找脚本，而是直接使用内嵌的脚本内容。
	targetScriptPath := filepath.Join(confDir, "alastor_enhanced.py")

	err := ioutil.WriteFile(targetScriptPath, []byte(alastorEnhancedScriptContent), 0755)
	if err != nil {
		fmt.Printf("[Alastor CRITICAL] 无法创建内嵌的mitmproxy脚本: %v\n", err)
		// 即使失败，也尝试返回路径，mitmdump可能会报告更详细的错误
		return targetScriptPath
	}

	fmt.Printf("[Alastor DEBUG] ✅ 成功创建内嵌的mitmproxy脚本: %s\n", targetScriptPath)
	return targetScriptPath
}
